{"info": {"_postman_id": "66a118f6-4ada-48da-a5a8-9fa06ffdf494", "name": "ZOHO Recruit", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON><PERSON> Refresh <PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "https://accounts.zoho.in/oauth/v2/token?refresh_token=**********************************************************************&client_id={{client_id}}&client_secret={{client_secret}}&grant_type=refresh_token&scope=ZohoRecruit.modules.all", "protocol": "https", "host": ["accounts", "zoho", "in"], "path": ["o<PERSON>h", "v2", "token"], "query": [{"key": "refresh_token", "value": "**********************************************************************"}, {"key": "client_id", "value": "{{client_id}}"}, {"key": "client_secret", "value": "{{client_secret}}"}, {"key": "grant_type", "value": "refresh_token"}, {"key": "scope", "value": "ZohoRecruit.modules.all"}]}}, "response": []}, {"name": "Job Opening", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Zoho-oauthtoken **********************************************************************", "type": "default"}], "url": {"raw": "https://recruit.zoho.in/recruit/private/json/JobOpenings/getRecords?fromIndex=0&toIndex=1000&&sortOrderString=asc", "protocol": "https", "host": ["recruit", "zoho", "in"], "path": ["recruit", "private", "json", "JobO<PERSON>ings", "getRecords"], "query": [{"key": "fromIndex", "value": "0"}, {"key": "toIndex", "value": "1000"}, {"key": null, "value": null}, {"key": "sortOrderString", "value": "asc"}]}}, "response": []}, {"name": "Candidate", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Zoho-oauthtoken **********************************************************************", "type": "default"}], "url": {"raw": "https://recruit.zoho.in/recruit/v2/Candidates?page=1&per_page=200&sort_by=Created_Time&sort_order=desc", "protocol": "https", "host": ["recruit", "zoho", "in"], "path": ["recruit", "v2", "Candidates"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "200"}, {"key": "sort_by", "value": "Created_Time"}, {"key": "sort_order", "value": "desc"}]}}, "response": []}, {"name": "Candidate-Application", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Zoho-oauthtoken **********************************************************************", "type": "default"}], "url": {"raw": "https://recruit.zoho.in/recruit/v2/Candidates?page=1&per_page=200&sort_by=Created_Time&sort_order=desc", "protocol": "https", "host": ["recruit", "zoho", "in"], "path": ["recruit", "v2", "Candidates"], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "200"}, {"key": "sort_by", "value": "Created_Time"}, {"key": "sort_order", "value": "desc"}]}}, "response": []}]}