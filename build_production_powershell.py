#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PowerShell-Compatible Production Build Script for Talent Hero v3.11
Uses PowerShell syntax for Windows compatibility
"""

import os
import subprocess
import sys
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def run_powershell_command(command, cwd=None, description=""):
    """Run a PowerShell command"""
    print(f"[INFO] {description}")
    try:
        if sys.platform.startswith('win'):
            # Use PowerShell explicitly
            ps_command = f'powershell.exe -Command "{command}"'
            result = subprocess.run(ps_command, shell=True, cwd=cwd, check=True, 
                                  capture_output=True, text=True)
        else:
            # Use bash for non-Windows
            result = subprocess.run(command, shell=True, cwd=cwd, check=True, 
                                  capture_output=True, text=True)
        print(f"[OK] {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} - Failed")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def switch_to_production():
    """Switch to production environment"""
    print("[INFO] Switching to production environment...")
    
    # Backend environment
    backend_dir = Path("backend")
    backend_prod_env = backend_dir / ".env.production"
    backend_env = backend_dir / ".env"
    
    if backend_prod_env.exists():
        import shutil
        shutil.copy2(backend_prod_env, backend_env)
        print("[OK] Backend: Switched to production configuration")
    else:
        print("[ERROR] Backend: Production .env.production not found")
        return False
    
    # Frontend environment
    frontend_dir = Path("frontend")
    frontend_prod_env = frontend_dir / ".env.production"
    frontend_env = frontend_dir / ".env"
    
    if frontend_prod_env.exists():
        import shutil
        shutil.copy2(frontend_prod_env, frontend_env)
        print("[OK] Frontend: Switched to production configuration")
    else:
        print("[ERROR] Frontend: Production .env.production not found")
        return False
    
    return True

def build_frontend():
    """Build frontend using PowerShell"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("[ERROR] Frontend directory not found")
        return False
    
    # Install dependencies
    if not run_powershell_command("npm install", cwd=frontend_dir, 
                                 description="Installing frontend dependencies"):
        return False
    
    # Build for production
    if not run_powershell_command("npm run build", cwd=frontend_dir,
                                 description="Building frontend for production"):
        return False
    
    return True

def prepare_backend():
    """Prepare backend using PowerShell"""
    backend_dir = Path("backend")
    
    if not backend_dir.exists():
        print("[ERROR] Backend directory not found")
        return False
    
    # Check if virtual environment exists
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("[INFO] Creating virtual environment...")
        if not run_powershell_command("python -m venv venv", cwd=backend_dir,
                                     description="Creating virtual environment"):
            return False
    
    # Install dependencies using PowerShell
    venv_activate = ".\\venv\\Scripts\\Activate.ps1"
    pip_command = f"{venv_activate}; pip install -r requirements.txt"
    
    if not run_powershell_command(pip_command, cwd=backend_dir,
                                 description="Installing backend dependencies"):
        return False
    
    # Collect static files
    collectstatic_command = f"{venv_activate}; python manage.py collectstatic --noinput"
    if not run_powershell_command(collectstatic_command, cwd=backend_dir,
                                 description="Collecting static files"):
        print("[WARN] Static files collection failed (this might be normal)")
    
    # Run migrations
    migrate_fake_command = f"{venv_activate}; python manage.py migrate ai_settings 0003 --fake"
    run_powershell_command(migrate_fake_command, cwd=backend_dir,
                          description="Faking problematic migration")
    
    migrate_command = f"{venv_activate}; python manage.py migrate"
    if not run_powershell_command(migrate_command, cwd=backend_dir,
                                 description="Running database migrations"):
        print("[WARN] Database migrations failed (database might not be running)")
    
    return True

def main():
    print("[INFO] Building Talent Hero v3.11 for production (PowerShell Compatible)...")
    
    # Step 1: Switch to production environment
    print("\n[STEP 1] Switching to production environment...")
    if not switch_to_production():
        print("[ERROR] Failed to switch to production environment")
        sys.exit(1)
    
    # Step 2: Build frontend
    print("\n[STEP 2] Building frontend...")
    if not build_frontend():
        print("[ERROR] Frontend build failed")
        sys.exit(1)
    
    # Step 3: Prepare backend
    print("\n[STEP 3] Preparing backend...")
    if not prepare_backend():
        print("[ERROR] Backend preparation failed")
        sys.exit(1)
    
    print("\n[SUCCESS] Production build completed successfully!")
    print("\n[INFO] Next steps:")
    print("   1. Configure your nginx server with the provided config")
    print("   2. Start the backend: cd backend; .\\venv\\Scripts\\Activate.ps1; python manage.py runserver 0.0.0.0:8000")
    print("   3. The frontend build is in frontend/dist/")
    print("   4. Access your application at: https://talenthero.bceglobaltech.com")

if __name__ == "__main__":
    main()
