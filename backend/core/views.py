from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import logging

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard(request):
    """Main dashboard view"""
    try:
        user = request.user

        # Dashboard data based on user type
        dashboard_data = {
            'user': {
                'id': str(user.id),
                'email': user.email,
                'employee_id': user.employee_id,
                'full_name': user.full_name,
                'user_type': user.user_type,
                'role_category': user.role_category,
                'department': user.department,
                'position': user.position,
            },
            'permissions': {
                'can_access_admin': user.is_admin,
                'can_manage_users': user.has_perm('accounts.can_manage_users'),
                'can_manage_admins': user.has_perm('accounts.can_manage_admins'),
                'can_view_admin_panel': user.has_perm('accounts.can_view_admin_panel'),
                'can_manage_vendors': user.has_perm('accounts.can_manage_vendors'),
            },
            'stats': {
                'total_users': 0,
                'active_users': 0,
                'total_admins': 0,
                'total_vendors': 0,
            }
        }

        # Add stats for admin users
        if user.is_admin:
            from accounts.models import User

            # Get counts for new metrics
            try:
                from job_management.models import JobRole, Job
                total_job_roles = JobRole.objects.filter(is_active=True).count()
                total_jobs = Job.objects.filter(is_active=True).count()
            except:
                total_job_roles = 0
                total_jobs = 0

            try:
                from candidate_management.models import Candidate
                total_candidates = Candidate.objects.count()
            except:
                total_candidates = 0

            try:
                from interviews.models import Interview
                total_interviews = Interview.objects.count()
            except:
                total_interviews = 0

            dashboard_data['stats'] = {
                'total_users': User.objects.count(),
                'active_users': User.objects.filter(is_active=True).count(),
                'total_admins': User.objects.filter(user_type__in=['superadmin', 'admin']).count(),
                'total_vendors': User.objects.filter(user_type='vendor').count(),
                'total_job_roles': total_job_roles,
                'total_jobs': total_jobs,
                'total_candidates': total_candidates,
                'total_interviews': total_interviews,
            }

        return Response({
            'success': True,
            'data': dashboard_data
        })

    except Exception as e:
        logger.error(f"Error in dashboard: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to load dashboard'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def home(request):
    """Home page view"""
    try:
        return Response({
            'success': True,
            'message': 'Welcome to Talent Hero v3.11',
            'user': {
                'full_name': request.user.full_name,
                'user_type': request.user.user_type,
            }
        })
    except Exception as e:
        logger.error(f"Error in home: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to load home page'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
