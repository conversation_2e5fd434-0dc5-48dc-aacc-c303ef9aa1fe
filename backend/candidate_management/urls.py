from django.urls import path
from . import views

app_name = 'candidate_management'

urlpatterns = [
    # Candidate management - specific paths first, then dynamic ones
    path('candidates/', views.list_candidates, name='list_candidates'),
    path('candidates/create/', views.create_candidate, name='create_candidate'),
    path('candidates/sync-from-zoho/', views.sync_candidates_from_zoho, name='sync_candidates_from_zoho'),
    path('candidates/bulk-upload/', views.bulk_upload_candidates, name='bulk_upload_candidates'),
    path('candidates/<str:candidate_id>/', views.get_candidate_details, name='get_candidate_details'),
    path('candidates/<str:candidate_id>/update/', views.update_candidate, name='update_candidate'),
    path('candidates/<str:candidate_id>/delete/', views.delete_candidate, name='delete_candidate'),
    path('candidates/<str:candidate_id>/profile-photo/', views.get_candidate_profile_photo, name='get_candidate_profile_photo'),
]
