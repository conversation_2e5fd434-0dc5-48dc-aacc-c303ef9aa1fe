from mongoengine import Document, <PERSON><PERSON>ield, IntField, DateTimeField, FileField, BooleanField, ListField, EmbeddedDocument, EmbeddedDocumentField
import datetime

class InterviewRound(EmbeddedDocument):
    """
    Interview Round embedded document for candidate interview tracking
    """
    round_name = StringField(required=True, max_length=50)  # L1, L2, L3, L4, L5, L6
    status = StringField(choices=['Scheduled', 'Attended', 'Hold', 'Rejected', 'Selected', 'Dropped-Out'], default='Scheduled')
    schedule_date = StringField(max_length=20)  # Date as string
    schedule_time = StringField(max_length=20)  # Time as string
    panel_name = StringField(max_length=200)
    panel_comment = StringField(max_length=1000)
    score = StringField(max_length=50)  # Can be numeric or text score
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)

class Candidate(Document):
    """
    Candidate model stored in MongoDB
    """
    # Basic Information
    full_name = StringField(required=True, max_length=200)
    phone_number = StringField(max_length=20)
    email = StringField(max_length=100)
    candidate_id = StringField(max_length=50, unique=True)
    city = StringField(max_length=100)
    application_type = StringField(choices=['Job Portal', 'Employee Referral', 'Vendor'])
    profile_photo = FileField()  # For uploaded profile photo
    
    # Job Related
    preferred_role = StringField()  # Reference to JobRole ID
    optional_roles = ListField(StringField())  # List of JobRole IDs
    total_experience = StringField(max_length=50)  # e.g., "3.5 years", "2-3 years"
    last_job_date = StringField(max_length=20)  # Date as string
    
    # Documents
    resume_file = FileField()  # For uploaded resume (PDF, DOC)
    resume_filename = StringField(max_length=200)  # Original filename
    
    # Additional Information
    comments = StringField(max_length=2000)
    
    # Interview Rounds
    interview_rounds = ListField(EmbeddedDocumentField(InterviewRound))
    
    # Status and Metadata
    status = StringField(choices=['Active', 'Inactive', 'Hired', 'Rejected', 'On-Hold'], default='Active')
    source = StringField(choices=['bulk_upload', 'manual_entry'], default='manual_entry')
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the candidate
    
    meta = {
        'collection': 'candidates',
        'indexes': [
            'full_name',
            'email',
            'phone_number',
            'candidate_id',
            'preferred_role',
            'status',
            'created_at',
            {'fields': ['$full_name', '$email', '$comments'],
             'default_language': 'english',
             'weights': {'full_name': 10, 'email': 5, 'comments': 1}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.full_name} ({self.candidate_id})"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Candidate, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert candidate to dictionary for API responses"""
        data = {
            'id': str(self.id),
            'full_name': self.full_name,
            'phone_number': self.phone_number,
            'email': self.email,
            'candidate_id': self.candidate_id,
            'city': self.city,
            'application_type': self.application_type,
            'has_profile_photo': bool(self.profile_photo),
            'preferred_role': self.preferred_role,
            'optional_roles': self.optional_roles,
            'total_experience': self.total_experience,
            'last_job_date': self.last_job_date,
            'resume_filename': self.resume_filename,
            'comments': self.comments,
            'status': self.status,
            'source': self.source,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
            'interview_rounds': []
        }
        
        # Convert interview rounds
        for round_data in self.interview_rounds:
            data['interview_rounds'].append({
                'round_name': round_data.round_name,
                'status': round_data.status,
                'schedule_date': round_data.schedule_date,
                'schedule_time': round_data.schedule_time,
                'panel_name': round_data.panel_name,
                'panel_comment': round_data.panel_comment,
                'score': round_data.score,
                'created_at': round_data.created_at.isoformat() if round_data.created_at else None,
                'updated_at': round_data.updated_at.isoformat() if round_data.updated_at else None,
            })
        
        return data
