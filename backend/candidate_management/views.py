import json
import logging
import pandas as pd
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from mongoengine.errors import ValidationError, DoesNotExist
from bson import ObjectId
from bson.errors import InvalidId

from accounts.models import User
from admin_controls.utils import check_admin_permission
from job_management.models import JobRole
from .models import Candidate, InterviewRound

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_candidates(request):
    """List all candidates with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')
        status_filter = request.GET.get('status', '')

        # Build query
        queryset = Candidate.objects()

        # Apply search filter
        if search:
            from mongoengine import Q
            queryset = queryset.filter(
                Q(full_name__icontains=search) | Q(candidate_id__icontains=search)
            )

        # Apply status filter
        if status_filter:
            queryset = queryset.filter(status=status_filter)

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        skip = (page - 1) * per_page
        candidates = queryset.skip(skip).limit(per_page).order_by('-created_at')

        # Convert to list of dictionaries with job role names
        candidate_data = []
        for candidate in candidates:
            candidate_dict = candidate.to_dict()

            # Get preferred role name
            if candidate.preferred_role:
                try:
                    from job_management.models import JobRole
                    job_role = JobRole.objects.get(id=candidate.preferred_role, is_active=True)
                    candidate_dict['preferred_role_name'] = job_role.role_name
                except:
                    candidate_dict['preferred_role_name'] = 'Unknown Role'
            else:
                candidate_dict['preferred_role_name'] = 'N/A'

            candidate_data.append(candidate_dict)

        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_previous = page > 1

        return Response({
            'success': True,
            'data': candidate_data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'has_next': has_next,
                'has_previous': has_previous,
                'per_page': per_page
            }
        })

    except Exception as e:
        logger.error(f"Error in list_candidates: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch candidates'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_candidate(request):
    """Create a new candidate"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        full_name = data.get('full_name')
        phone_number = data.get('phone_number', '')
        email = data.get('email', '')
        candidate_id = data.get('candidate_id', '')
        preferred_role = data.get('preferred_role', '')

        # Parse optional_roles if it's a JSON string
        optional_roles = data.get('optional_roles', [])
        if isinstance(optional_roles, str):
            try:
                optional_roles = json.loads(optional_roles)
            except json.JSONDecodeError:
                optional_roles = []

        total_experience = data.get('total_experience', '')
        last_job_date = data.get('last_job_date', '')
        comments = data.get('comments', '')

        # Parse interview_rounds if it's a JSON string
        interview_rounds_data = data.get('interview_rounds', [])
        if isinstance(interview_rounds_data, str):
            try:
                interview_rounds_data = json.loads(interview_rounds_data)
            except json.JSONDecodeError:
                interview_rounds_data = []

        if not full_name:
            return Response({
                'success': False,
                'error': 'Full name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate preferred role if provided
        if preferred_role:
            try:
                JobRole.objects.get(id=preferred_role, is_active=True)
            except (DoesNotExist, InvalidId):
                return Response({
                    'success': False,
                    'error': 'Invalid preferred role selected'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Validate optional roles if provided
        valid_optional_roles = []
        for role_id in optional_roles:
            if role_id != preferred_role:  # Can't select same role as preferred
                try:
                    JobRole.objects.get(id=role_id, is_active=True)
                    valid_optional_roles.append(role_id)
                except (DoesNotExist, InvalidId):
                    pass  # Skip invalid roles

        # Create interview rounds
        interview_rounds = []
        for round_data in interview_rounds_data:
            interview_round = InterviewRound(
                round_name=round_data.get('round_name', 'L1 Round'),
                status=round_data.get('status', 'Scheduled'),
                schedule_date=round_data.get('schedule_date', ''),
                schedule_time=round_data.get('schedule_time', ''),
                panel_name=round_data.get('panel_name', ''),
                panel_comment=round_data.get('panel_comment', ''),
                score=round_data.get('score', '')
            )
            interview_rounds.append(interview_round)

        # If no interview rounds provided, create default L1 Round
        if not interview_rounds:
            interview_rounds.append(InterviewRound(
                round_name='L1 Round',
                status='Scheduled'
            ))

        # Get additional fields
        city = data.get('city', '')
        application_type = data.get('application_type', '')

        # Create candidate
        candidate = Candidate(
            full_name=full_name,
            phone_number=phone_number,
            email=email,
            candidate_id=candidate_id,
            city=city,
            application_type=application_type,
            preferred_role=preferred_role,
            optional_roles=valid_optional_roles,
            total_experience=total_experience,
            last_job_date=last_job_date,
            comments=comments,
            interview_rounds=interview_rounds,
            created_by=str(request.user.id)
        )
        # Handle profile photo upload
        if 'profile_photo' in request.FILES:
            candidate.profile_photo = request.FILES['profile_photo']

        candidate.save()

        logger.info(f"Candidate created: {candidate.full_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Candidate created successfully',
            'candidate': candidate.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_candidate: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create candidate'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def bulk_upload_candidates(request):
    """Bulk upload candidates from Excel/CSV file"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if 'file' not in request.FILES:
            return Response({
                'success': False,
                'error': 'No file provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        file = request.FILES['file']

        # Check file extension
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return Response({
                'success': False,
                'error': 'Only Excel (.xlsx, .xls) and CSV files are supported'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Read file
        try:
            if file.name.endswith('.csv'):
                df = pd.read_csv(file)
            else:
                df = pd.read_excel(file)
        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error reading file: {str(e)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Validate required columns
        required_columns = ['full_name']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            return Response({
                'success': False,
                'error': f'Missing required columns: {", ".join(missing_columns)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Process candidates
        created_candidates = []
        errors = []

        for index, row in df.iterrows():
            try:
                # Create candidate from row data
                candidate = Candidate(
                    full_name=str(row.get('full_name', '')).strip(),
                    phone_number=str(row.get('phone_number', '')).strip(),
                    email=str(row.get('email', '')).strip(),
                    candidate_id=str(row.get('candidate_id', '')).strip(),
                    preferred_role=str(row.get('preferred_role', '')).strip(),
                    total_experience=str(row.get('total_experience', '')).strip(),
                    last_job_date=str(row.get('last_job_date', '')).strip(),
                    comments=str(row.get('comments', '')).strip(),
                    source='bulk_upload',
                    created_by=str(request.user.id)
                )

                # Add default L1 Round
                candidate.interview_rounds = [InterviewRound(
                    round_name='L1 Round',
                    status='Scheduled'
                )]

                candidate.save()
                created_candidates.append(candidate.to_dict())

            except Exception as e:
                errors.append(f"Row {index + 2}: {str(e)}")

        return Response({
            'success': True,
            'message': f'Successfully created {len(created_candidates)} candidates',
            'created_count': len(created_candidates),
            'error_count': len(errors),
            'errors': errors[:10],  # Limit to first 10 errors
            'candidates': created_candidates
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"Error in bulk_upload_candidates: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to process bulk upload'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def delete_candidate(request, candidate_id):
    """Delete a candidate"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Candidate not found'
            }, status=status.HTTP_404_NOT_FOUND)

        candidate_name = candidate.full_name
        candidate.delete()

        logger.info(f"Candidate deleted: {candidate_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Candidate {candidate_name} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error in delete_candidate: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to delete candidate'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def sync_candidates_from_zoho(request):
    """Sync candidates from Zoho Recruit API"""
    try:
        from admin_controls.utils import check_admin_permission

        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        from job_management.zoho_service import ZohoAPIService

        logger.info(f"Starting Zoho candidate sync requested by {request.user.email}")

        # Initialize Zoho service
        zoho_service = ZohoAPIService()

        # Fetch candidates from Zoho
        zoho_data = zoho_service.fetch_candidates()

        if not zoho_data:
            logger.error("Failed to fetch candidate data from Zoho API")
            return Response({
                'success': False,
                'error': 'Failed to fetch candidate data from Zoho API'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Parse the response
        try:
            candidates = zoho_data.get('data', [])

            logger.info(f"Processing {len(candidates)} candidates from Zoho")

            created_count = 0
            updated_count = 0
            skipped_count = 0
            errors = []

            for zoho_candidate in candidates:
                try:
                    # Parse candidate data
                    parsed_candidate = zoho_service.parse_candidate_data(zoho_candidate)

                    if not parsed_candidate:
                        skipped_count += 1
                        continue

                    # Check if candidate already exists
                    existing_candidate = None
                    try:
                        existing_candidate = Candidate.objects.get(candidate_id=parsed_candidate['candidate_id'])
                        logger.info(f"Found existing candidate: {parsed_candidate['candidate_id']}")
                    except Candidate.DoesNotExist:
                        logger.info(f"New candidate: {parsed_candidate['candidate_id']}")

                    # Create or update candidate
                    if existing_candidate:
                        # Update existing candidate
                        existing_candidate.full_name = parsed_candidate['full_name']
                        existing_candidate.phone_number = parsed_candidate['phone_number']
                        existing_candidate.email = parsed_candidate['email']
                        existing_candidate.city = parsed_candidate['city']
                        existing_candidate.application_type = parsed_candidate['application_type']
                        existing_candidate.total_experience = parsed_candidate['total_experience']
                        existing_candidate.last_job_date = parsed_candidate['last_job_date']
                        existing_candidate.comments = parsed_candidate['comments']
                        existing_candidate.status = parsed_candidate['status']

                        existing_candidate.save()
                        updated_count += 1
                        logger.info(f"Updated candidate: {parsed_candidate['candidate_id']}")

                    else:
                        # Create new candidate
                        new_candidate = Candidate(
                            full_name=parsed_candidate['full_name'],
                            phone_number=parsed_candidate['phone_number'],
                            email=parsed_candidate['email'],
                            candidate_id=parsed_candidate['candidate_id'],
                            city=parsed_candidate['city'],
                            application_type=parsed_candidate['application_type'],
                            total_experience=parsed_candidate['total_experience'],
                            last_job_date=parsed_candidate['last_job_date'],
                            comments=parsed_candidate['comments'],
                            status=parsed_candidate['status'],
                            source=parsed_candidate['source']
                        )

                        new_candidate.save()
                        created_count += 1
                        logger.info(f"Created candidate: {parsed_candidate['candidate_id']}")

                except Exception as e:
                    error_msg = f"Error processing candidate: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    skipped_count += 1

            logger.info(f"Zoho candidate sync completed. Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}")

            return Response({
                'success': True,
                'message': f'Candidate sync completed successfully',
                'data': {
                    'created': created_count,
                    'updated': updated_count,
                    'skipped': skipped_count,
                    'total_processed': len(candidates),
                    'errors': errors[:5]  # Limit error messages
                }
            })

        except Exception as e:
            logger.error(f"Error processing Zoho candidate response: {str(e)}")
            return Response({
                'success': False,
                'error': f'Error processing Zoho candidate response: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        logger.error(f"Error in sync_candidates_from_zoho: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to sync candidates from Zoho'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_candidate_details(request, candidate_id):
    """Get candidate details by ID"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Candidate not found'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': True,
            'data': candidate.to_dict()
        })

    except Exception as e:
        logger.error(f"Error in get_candidate_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch candidate details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_candidate(request, candidate_id):
    """Update candidate details"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Candidate not found'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update basic information
        if 'full_name' in data:
            candidate.full_name = data['full_name'].strip()
        if 'phone_number' in data:
            candidate.phone_number = data['phone_number'].strip()
        if 'email' in data:
            candidate.email = data['email'].strip()
        if 'city' in data:
            candidate.city = data['city'].strip()
        if 'application_type' in data:
            candidate.application_type = data['application_type']
        if 'total_experience' in data:
            candidate.total_experience = data['total_experience'].strip()
        if 'last_job_date' in data:
            candidate.last_job_date = data['last_job_date'].strip()
        if 'comments' in data:
            candidate.comments = data['comments'].strip()
        if 'status' in data:
            candidate.status = data['status']

        # Update job roles
        if 'preferred_role' in data:
            candidate.preferred_role = data['preferred_role']
        if 'optional_roles' in data:
            # Parse JSON string if it's a string, otherwise use as-is
            optional_roles_data = data['optional_roles']
            if isinstance(optional_roles_data, str):
                try:
                    optional_roles_data = json.loads(optional_roles_data)
                except json.JSONDecodeError:
                    optional_roles_data = []
            candidate.optional_roles = optional_roles_data

        # Update interview rounds if provided
        if 'interview_rounds' in data:
            # Parse JSON string if it's a string, otherwise use as-is
            interview_rounds_data = data['interview_rounds']
            if isinstance(interview_rounds_data, str):
                try:
                    interview_rounds_data = json.loads(interview_rounds_data)
                except json.JSONDecodeError:
                    interview_rounds_data = []

            interview_rounds = []
            for round_data in interview_rounds_data:
                interview_round = InterviewRound(
                    round_name=round_data.get('round_name', ''),
                    status=round_data.get('status', 'Scheduled'),
                    schedule_date=round_data.get('schedule_date', ''),
                    schedule_time=round_data.get('schedule_time', ''),
                    panel_name=round_data.get('panel_name', ''),
                    panel_comment=round_data.get('panel_comment', ''),
                    score=round_data.get('score', '')
                )
                interview_rounds.append(interview_round)
            candidate.interview_rounds = interview_rounds

        # Handle profile photo upload
        if 'profile_photo' in request.FILES:
            candidate.profile_photo = request.FILES['profile_photo']

        candidate.save()

        logger.info(f"Candidate updated: {candidate.full_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Candidate updated successfully',
            'data': candidate.to_dict()
        })

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_candidate: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update candidate'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_candidate_profile_photo(request, candidate_id):
    """Get candidate profile photo"""
    try:
        if not check_admin_permission(request.user):
            return HttpResponse(status=403)

        try:
            candidate = Candidate.objects.get(id=candidate_id)
        except (DoesNotExist, InvalidId):
            return HttpResponse(status=404)

        if not candidate.profile_photo:
            return HttpResponse(status=404)

        # Return the profile photo
        response = HttpResponse(candidate.profile_photo.read(), content_type='image/jpeg')
        response['Content-Disposition'] = f'inline; filename="profile_{candidate_id}.jpg"'
        return response

    except Exception as e:
        logger.error(f"Error in get_candidate_profile_photo: {str(e)}")
        return HttpResponse(status=500)
