# Database Configuration
POSTGRES_DB=th
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

MONGODB_DB=th
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/

# Django Configuration
SECRET_KEY=django-insecure-your-secret-key-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=talenthero.bceglobaltech.com

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
FRONTEND_URL=https://talenthero.bceglobaltech.com

# Production Domain Configuration
PRODUCTION_DOMAIN=talenthero.bceglobaltech.com
PRODUCTION_FRONTEND_URL=https://talenthero.bceglobaltech.com
PRODUCTION_BACKEND_URL=https://talenthero.bceglobaltech.com/api

# Security
CORS_ALLOWED_ORIGINS=https://talenthero.bceglobaltech.com
CSRF_TRUSTED_ORIGINS=https://talenthero.bceglobaltech.com
CSRF_COOKIE_SECURE=True
SESSION_COOKIE_SECURE=True

# Session Configuration
SESSION_COOKIE_AGE=86400
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

client_id=1000.80P8NXCX0O1UE7SWVRXK2TW65ZI82C
client_secret=e66233b30492f2696347e6b3fd2962eaf189715b38
