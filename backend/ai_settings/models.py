from django.db import models
from django.conf import settings
import base64
import os
import hashlib

# Try to import cryptography, fallback to simple base64 if not available
try:
    from cryptography.fernet import Fernet
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False
    print("Warning: cryptography module not available. Using simple base64 encoding for API keys.")


class AIProviderConfig(models.Model):
    """Model to store individual AI provider configurations"""

    LLM_OPERATORS = [
        ('Ollama', 'Ollama'),
        ('Groq', 'Groq'),
        ('<PERSON>', 'Claude'),
        ('Open Router', 'Open Router'),
    ]

    provider_name = models.CharField(max_length=50, choices=LLM_OPERATORS, unique=True)
    encrypted_api_key = models.TextField(blank=True, null=True)
    llm_url = models.URLField()
    selected_model = models.CharField(max_length=200, blank=True)
    available_models = models.JSONField(default=list, blank=True)
    is_configured = models.Bo<PERSON>anField(default=False)

    # System settings
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "AI Provider Configuration"
        verbose_name_plural = "AI Provider Configurations"

    def __str__(self):
        return f"{self.provider_name} - {'Configured' if self.is_configured else 'Not Configured'}"

    @staticmethod
    def get_encryption_key():
        """Get or create encryption key"""
        if not CRYPTOGRAPHY_AVAILABLE:
            # Simple fallback key for base64 encoding
            return b'simple_fallback_key_for_demo_purposes_only'

        key_file = os.path.join(settings.BASE_DIR, '.ai_encryption_key')

        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            # Generate new key
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            return key

    def set_api_key(self, api_key):
        """Encrypt and store API key"""
        if not api_key:
            self.encrypted_api_key = None
            return

        if CRYPTOGRAPHY_AVAILABLE:
            key = self.get_encryption_key()
            fernet = Fernet(key)
            encrypted_key = fernet.encrypt(api_key.encode())
            self.encrypted_api_key = base64.b64encode(encrypted_key).decode()
        else:
            # Simple base64 encoding as fallback (not secure, for demo only)
            encoded_key = base64.b64encode(api_key.encode()).decode()
            self.encrypted_api_key = encoded_key

    def get_api_key(self):
        """Decrypt and return API key"""
        if not self.encrypted_api_key:
            return None

        try:
            if CRYPTOGRAPHY_AVAILABLE:
                key = self.get_encryption_key()
                fernet = Fernet(key)
                encrypted_key = base64.b64decode(self.encrypted_api_key.encode())
                return fernet.decrypt(encrypted_key).decode()
            else:
                # Simple base64 decoding as fallback
                return base64.b64decode(self.encrypted_api_key.encode()).decode()
        except Exception:
            return None


class AISettings(models.Model):
    """Model to store AI system settings and active provider selection"""

    active_provider = models.CharField(max_length=50, default='Ollama')
    enable_request_queue = models.BooleanField(default=True)
    max_concurrent_requests = models.IntegerField(default=3)
    request_timeout = models.IntegerField(default=30)  # seconds

    # System settings
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "AI Settings"
        verbose_name_plural = "AI Settings"
    
    def __str__(self):
        return f"AI Settings - {self.active_provider}"
    
    @classmethod
    def get_current_settings(cls):
        """Get the current active AI settings"""
        settings_obj, created = cls.objects.get_or_create(
            is_active=True,
            defaults={
                'active_provider': 'Ollama',
                'enable_request_queue': True,
                'max_concurrent_requests': 3,
                'request_timeout': 30
            }
        )
        return settings_obj

    def get_active_provider_config(self):
        """Get the configuration for the currently active provider"""
        try:
            return AIProviderConfig.objects.get(provider_name=self.active_provider)
        except AIProviderConfig.DoesNotExist:
            return None

    @classmethod
    def get_all_provider_configs(cls):
        """Get all provider configurations"""
        configs = {}
        for provider_choice in AIProviderConfig.LLM_OPERATORS:
            provider_name = provider_choice[0]
            try:
                config = AIProviderConfig.objects.get(provider_name=provider_name)
                configs[provider_name] = config
            except AIProviderConfig.DoesNotExist:
                # Create default config
                default_urls = {
                    'Ollama': 'http://localhost:11434',
                    'Groq': 'https://api.groq.com/openai/v1',
                    'Claude': 'https://api.anthropic.com/v1',
                    'Open Router': 'https://openrouter.ai/api/v1'
                }
                config = AIProviderConfig.objects.create(
                    provider_name=provider_name,
                    llm_url=default_urls.get(provider_name, ''),
                    is_configured=False
                )
                configs[provider_name] = config
        return configs


class SystemSettings(models.Model):
    """Model to store general system settings"""
    
    # General Settings
    system_name = models.CharField(max_length=200, default='Talent Hero v3.11')
    system_description = models.TextField(default='Comprehensive talent acquisition and management platform')
    timezone = models.CharField(max_length=50, default='UTC')
    date_format = models.CharField(max_length=20, default='YYYY-MM-DD')
    
    # Security Settings
    session_timeout = models.IntegerField(default=30)  # minutes
    password_min_length = models.IntegerField(default=8)
    require_special_chars = models.BooleanField(default=True)
    enable_two_factor = models.BooleanField(default=False)
    
    # Notification Settings
    enable_email_notifications = models.BooleanField(default=False)
    enable_job_alerts = models.BooleanField(default=False)
    enable_candidate_alerts = models.BooleanField(default=False)
    enable_interview_reminders = models.BooleanField(default=False)
    
    # Data Management Settings
    data_retention_days = models.IntegerField(default=365)
    enable_data_backup = models.BooleanField(default=True)
    backup_frequency = models.CharField(max_length=20, default='daily')
    
    # Workflow Settings
    enable_workflow_automation = models.BooleanField(default=True)
    default_workflow_timeout = models.IntegerField(default=24)  # hours
    
    # Scheduling Settings
    default_meeting_duration = models.IntegerField(default=60)  # minutes
    working_hours_start = models.TimeField(default='09:00')
    working_hours_end = models.TimeField(default='18:00')
    
    # System metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = "System Settings"
        verbose_name_plural = "System Settings"
    
    def __str__(self):
        return f"System Settings - {self.system_name}"
    
    @classmethod
    def get_current_settings(cls):
        """Get the current active system settings"""
        settings_obj, created = cls.objects.get_or_create(
            is_active=True,
            defaults={
                'system_name': 'Talent Hero v3.11',
                'system_description': 'Comprehensive talent acquisition and management platform',
                'timezone': 'UTC',
                'date_format': 'YYYY-MM-DD'
            }
        )
        return settings_obj
