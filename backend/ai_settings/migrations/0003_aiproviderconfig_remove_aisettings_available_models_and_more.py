# Generated by Django 5.2.4 on 2025-08-16 07:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ai_settings', '0002_migrate_to_new_structure'),
    ]

    operations = [
        migrations.CreateModel(
            name='AIProviderConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('provider_name', models.CharField(choices=[('Ollama', 'Ollama'), ('Groq', 'Groq'), ('<PERSON>', 'Claude'), ('Open Router', 'Open Router')], max_length=50, unique=True)),
                ('encrypted_api_key', models.TextField(blank=True, null=True)),
                ('llm_url', models.URLField()),
                ('selected_model', models.CharField(blank=True, max_length=200)),
                ('available_models', models.J<PERSON>NField(blank=True, default=list)),
                ('is_configured', models.<PERSON><PERSON>anField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'AI Provider Configuration',
                'verbose_name_plural': 'AI Provider Configurations',
            },
        ),
        migrations.RemoveField(
            model_name='aisettings',
            name='available_models',
        ),
        migrations.RemoveField(
            model_name='aisettings',
            name='encrypted_api_key',
        ),
        migrations.RemoveField(
            model_name='aisettings',
            name='llm_operator',
        ),
        migrations.RemoveField(
            model_name='aisettings',
            name='llm_url',
        ),
        migrations.RemoveField(
            model_name='aisettings',
            name='selected_model',
        ),
        migrations.AddField(
            model_name='aisettings',
            name='active_provider',
            field=models.CharField(default='Ollama', max_length=50),
        ),
        migrations.AddField(
            model_name='aisettings',
            name='enable_request_queue',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='aisettings',
            name='max_concurrent_requests',
            field=models.IntegerField(default=3),
        ),
        migrations.AddField(
            model_name='aisettings',
            name='request_timeout',
            field=models.IntegerField(default=30),
        ),
    ]
