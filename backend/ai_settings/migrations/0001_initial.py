# Generated by Django 5.2.4 on 2025-08-14 18:59

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AISettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('llm_operator', models.CharField(choices=[('Ollama', 'Ollama'), ('Groq', 'Groq'), ('<PERSON>', '<PERSON>'), ('Open Router', 'Open Router')], default='Ollama', max_length=50)),
                ('encrypted_api_key', models.TextField(blank=True, null=True)),
                ('llm_url', models.URLField(default='http://localhost:11434')),
                ('selected_model', models.CharField(blank=True, max_length=200)),
                ('available_models', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'AI Settings',
                'verbose_name_plural': 'AI Settings',
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_name', models.CharField(default='Talent Hero v3.11', max_length=200)),
                ('system_description', models.TextField(default='Comprehensive talent acquisition and management platform')),
                ('timezone', models.CharField(default='UTC', max_length=50)),
                ('date_format', models.CharField(default='YYYY-MM-DD', max_length=20)),
                ('session_timeout', models.IntegerField(default=30)),
                ('password_min_length', models.IntegerField(default=8)),
                ('require_special_chars', models.BooleanField(default=True)),
                ('enable_two_factor', models.BooleanField(default=False)),
                ('enable_email_notifications', models.BooleanField(default=False)),
                ('enable_job_alerts', models.BooleanField(default=False)),
                ('enable_candidate_alerts', models.BooleanField(default=False)),
                ('enable_interview_reminders', models.BooleanField(default=False)),
                ('data_retention_days', models.IntegerField(default=365)),
                ('enable_data_backup', models.BooleanField(default=True)),
                ('backup_frequency', models.CharField(default='daily', max_length=20)),
                ('enable_workflow_automation', models.BooleanField(default=True)),
                ('default_workflow_timeout', models.IntegerField(default=24)),
                ('default_meeting_duration', models.IntegerField(default=60)),
                ('working_hours_start', models.TimeField(default='09:00')),
                ('working_hours_end', models.TimeField(default='18:00')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'System Settings',
                'verbose_name_plural': 'System Settings',
            },
        ),
    ]
