# Migration to handle transition from old AI settings to new multi-provider structure

from django.db import migrations, models


def migrate_old_settings(apps, schema_editor):
    """Migrate any existing AI settings to new structure"""
    try:
        # This is a safe migration that creates default settings
        AISettings = apps.get_model('ai_settings', 'AISettings')
        AIProviderConfig = apps.get_model('ai_settings', 'AIProviderConfig')
        
        # Create default AI settings if none exist
        if not AISettings.objects.exists():
            AISettings.objects.create(
                active_provider='Ollama',
                enable_request_queue=True,
                max_concurrent_requests=3,
                request_timeout=30,
                is_active=True
            )
        
        # Create default provider configs if none exist
        default_providers = {
            'Ollama': 'http://localhost:11434',
            'Groq': 'https://api.groq.com/openai/v1',
            'Claude': 'https://api.anthropic.com/v1',
            'Open Router': 'https://openrouter.ai/api/v1'
        }
        
        for provider_name, default_url in default_providers.items():
            if not AIProviderConfig.objects.filter(provider_name=provider_name).exists():
                AIProviderConfig.objects.create(
                    provider_name=provider_name,
                    llm_url=default_url,
                    is_configured=False
                )
                
    except Exception as e:
        # If migration fails, it's okay - the models will create defaults
        print(f"Migration note: {e}")


def reverse_migrate(apps, schema_editor):
    """Reverse migration - clean up if needed"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('ai_settings', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(migrate_old_settings, reverse_migrate),
    ]
