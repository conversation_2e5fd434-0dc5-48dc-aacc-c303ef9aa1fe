from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
import json
import logging

# Try to import requests, provide fallback if not available
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Warning: requests module not available. Connection testing will be disabled.")
from .models import AISettings, SystemSettings, AIProviderConfig
from .queue import get_queue_status, queue_llm_request
from admin_controls.utils import check_admin_permission

logger = logging.getLogger(__name__)


def ensure_all_providers_exist():
    """Ensure all provider configurations exist in database"""
    default_configs = {
        'Ollama': 'http://localhost:11434',
        'Groq': 'https://api.groq.com/openai/v1',
        '<PERSON>': 'https://api.anthropic.com/v1',
        'Open Router': 'https://openrouter.ai/api/v1'
    }

    for provider_name, default_url in default_configs.items():
        try:
            config, created = AIProviderConfig.objects.get_or_create(
                provider_name=provider_name,
                defaults={
                    'llm_url': default_url,
                    'is_configured': False
                }
            )
            if created:
                logger.info(f"Created default configuration for {provider_name}")
        except Exception as e:
            logger.error(f"Error ensuring provider {provider_name} exists: {str(e)}")


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def ai_settings_view(request):
    """Handle AI settings GET and POST requests"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if request.method == 'GET':
            # Get current AI settings and all provider configs
            ai_settings = AISettings.get_current_settings()

            # Ensure all providers exist in database
            ensure_all_providers_exist()

            # Fix Ollama configuration if needed
            try:
                ollama_config = AIProviderConfig.objects.get(provider_name='Ollama')
                if ollama_config.llm_url and not ollama_config.is_configured:
                    # Test if Ollama is actually working with longer timeout
                    if REQUESTS_AVAILABLE:
                        try:
                            import requests
                            response = requests.get(f"{ollama_config.llm_url}/api/tags", timeout=15)
                            if response.status_code == 200:
                                # Fetch and save available models
                                models_data = response.json()
                                models = [model['name'] for model in models_data.get('models', [])]
                                ollama_config.available_models = models

                                # Set default model if none selected
                                if not ollama_config.selected_model and models:
                                    ollama_config.selected_model = models[0]

                                ollama_config.is_configured = True
                                ollama_config.save()
                                logger.info(f"Auto-fixed Ollama configuration - marked as configured with models: {models}")
                        except Exception as e:
                            logger.warning(f"Failed to auto-configure Ollama: {str(e)}")
            except AIProviderConfig.DoesNotExist:
                pass

            provider_configs = AISettings.get_all_provider_configs()

            # Format provider configs for frontend
            providers = {}
            for provider_name, config in provider_configs.items():
                providers[provider_name] = {
                    'provider_name': config.provider_name,
                    'api_key': '***' if config.get_api_key() else '',  # Masked for security
                    'llm_url': config.llm_url,
                    'selected_model': config.selected_model,
                    'available_models': config.available_models,
                    'is_configured': config.is_configured
                }

            return Response({
                'success': True,
                'data': {
                    'active_provider': ai_settings.active_provider,
                    'enable_request_queue': ai_settings.enable_request_queue,
                    'max_concurrent_requests': ai_settings.max_concurrent_requests,
                    'request_timeout': ai_settings.request_timeout,
                    'providers': providers,
                    'queue_status': get_queue_status()
                }
            })

        elif request.method == 'POST':
            # Update AI settings
            data = json.loads(request.body)
            ai_settings = AISettings.get_current_settings()

            # Update general AI settings
            ai_settings.active_provider = data.get('active_provider', ai_settings.active_provider)
            ai_settings.enable_request_queue = data.get('enable_request_queue', ai_settings.enable_request_queue)
            ai_settings.max_concurrent_requests = data.get('max_concurrent_requests', ai_settings.max_concurrent_requests)
            ai_settings.request_timeout = data.get('request_timeout', ai_settings.request_timeout)
            ai_settings.save()

            # Update the active provider configuration (for backward compatibility with frontend)
            active_provider = data.get('active_provider', ai_settings.active_provider)
            if active_provider:
                try:
                    config, created = AIProviderConfig.objects.get_or_create(
                        provider_name=active_provider,
                        defaults={'llm_url': data.get('llm_url', '')}
                    )

                    # Update fields from flat structure
                    if 'llm_url' in data:
                        config.llm_url = data['llm_url']
                    if 'selected_model' in data:
                        config.selected_model = data['selected_model']
                    if 'available_models' in data:
                        config.available_models = data['available_models']

                    # Handle API key encryption
                    api_key = data.get('api_key', '')
                    if api_key and api_key != '***':  # Only update if not masked
                        config.set_api_key(api_key)
                        config.is_configured = True
                    elif active_provider.lower() == 'ollama':
                        # Ollama doesn't require API key, mark as configured if URL is set
                        if config.llm_url:
                            config.is_configured = True

                    config.save()

                except Exception as e:
                    logger.error(f"Error updating active provider {active_provider}: {str(e)}")

            # Also handle nested provider data if provided
            providers_data = data.get('providers', {})
            for provider_name, provider_data in providers_data.items():
                try:
                    config, created = AIProviderConfig.objects.get_or_create(
                        provider_name=provider_name,
                        defaults={'llm_url': provider_data.get('llm_url', '')}
                    )

                    # Update fields
                    config.llm_url = provider_data.get('llm_url', config.llm_url)
                    config.selected_model = provider_data.get('selected_model', config.selected_model)
                    config.available_models = provider_data.get('available_models', config.available_models)

                    # Handle API key encryption
                    api_key = provider_data.get('api_key', '')
                    if api_key and api_key != '***':  # Only update if not masked
                        config.set_api_key(api_key)
                        config.is_configured = True
                    elif provider_name.lower() == 'ollama':
                        # Ollama doesn't require API key, mark as configured if URL is set
                        if config.llm_url:
                            config.is_configured = True

                    config.save()

                except Exception as e:
                    logger.error(f"Error updating provider {provider_name}: {str(e)}")

        # Special handling for Ollama - mark as configured if it's the active provider and has URL
        if active_provider and active_provider.lower() == 'ollama':
            try:
                ollama_config = AIProviderConfig.objects.get(provider_name='Ollama')
                if ollama_config.llm_url and not ollama_config.is_configured:
                    ollama_config.is_configured = True
                    ollama_config.save()
                    logger.info("Auto-configured Ollama provider")
            except AIProviderConfig.DoesNotExist:
                logger.warning("Ollama configuration not found")

        return Response({
            'success': True,
            'message': 'AI settings saved successfully'
        })

    except Exception as e:
        logger.error(f"Error in ai_settings_view: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to process AI settings'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def test_llm_connection(request):
    """Test connection to LLM provider"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if not REQUESTS_AVAILABLE:
            return Response({
                'success': False,
                'error': 'Connection testing not available. Please install requests module.'
            })

        data = json.loads(request.body)
        # Accept both provider_name and llm_operator for backward compatibility
        provider_name = data.get('provider_name') or data.get('llm_operator')
        llm_url = data.get('llm_url')
        api_key = data.get('api_key')

        if provider_name == 'Ollama':
            # Test Ollama connection and fetch models
            try:
                response = requests.get(f"{llm_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    models_data = response.json()
                    models = [model['name'] for model in models_data.get('models', [])]

                    # Mark Ollama as configured since connection is successful
                    try:
                        config, created = AIProviderConfig.objects.get_or_create(
                            provider_name='Ollama',
                            defaults={'llm_url': llm_url, 'is_configured': True}
                        )
                        if not created:
                            config.llm_url = llm_url
                            config.is_configured = True
                            config.available_models = models
                            config.save()
                        logger.info("Marked Ollama as configured after successful connection test")
                    except Exception as e:
                        logger.error(f"Error updating Ollama configuration: {str(e)}")

                    return Response({
                        'success': True,
                        'message': 'Connection successful',
                        'available_models': models
                    })
                else:
                    return Response({
                        'success': False,
                        'error': f'Failed to connect to Ollama: HTTP {response.status_code}'
                    })
            except requests.exceptions.RequestException as e:
                return Response({
                    'success': False,
                    'error': f'Connection failed: {str(e)}'
                })

        elif provider_name == 'Groq':
            # Test Groq connection
            try:
                headers = {
                    'Authorization': f'Bearer {api_key}',
                    'Content-Type': 'application/json'
                }
                response = requests.get(f"{llm_url}/models", headers=headers, timeout=10)
                if response.status_code == 200:
                    return Response({
                        'success': True,
                        'message': 'Connection successful',
                        'available_models': ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768']
                    })
                else:
                    return Response({
                        'success': False,
                        'error': f'Failed to connect to Groq: HTTP {response.status_code}'
                    })
            except requests.exceptions.RequestException as e:
                return Response({
                    'success': False,
                    'error': f'Connection failed: {str(e)}'
                })

        elif provider_name == 'Claude':
            # Test Claude connection (simplified)
            if not api_key:
                return Response({
                    'success': False,
                    'error': 'API key required for Claude'
                })
            
            return Response({
                'success': True,
                'message': 'Connection test successful (mock)',
                'available_models': ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229']
            })

        elif provider_name == 'Open Router':
            # Test Open Router connection (simplified)
            if not api_key:
                return Response({
                    'success': False,
                    'error': 'API key required for Open Router'
                })
            
            return Response({
                'success': True,
                'message': 'Connection test successful (mock)',
                'available_models': ['openai/gpt-4-turbo', 'anthropic/claude-3-sonnet', 'meta-llama/llama-3-8b-instruct']
            })

        else:
            return Response({
                'success': False,
                'error': 'Unsupported LLM operator'
            })

    except Exception as e:
        logger.error(f"Error in test_llm_connection: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to test connection'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def fetch_models(request):
    """Fetch available models from a provider"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if not REQUESTS_AVAILABLE:
            return Response({
                'success': False,
                'error': 'Model fetching not available. Please install requests module.'
            })

        data = json.loads(request.body)
        provider_name = data.get('provider_name') or data.get('llm_operator')
        llm_url = data.get('llm_url')

        if provider_name == 'Ollama':
            try:
                response = requests.get(f"{llm_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    models_data = response.json()
                    models = [model['name'] for model in models_data.get('models', [])]

                    return Response({
                        'success': True,
                        'models': models
                    })
                else:
                    return Response({
                        'success': False,
                        'error': f'Failed to fetch models: HTTP {response.status_code}'
                    })
            except requests.exceptions.RequestException as e:
                return Response({
                    'success': False,
                    'error': f'Failed to fetch models: {str(e)}'
                })
        else:
            # For other providers, return predefined models
            predefined_models = {
                'Groq': ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768'],
                'Claude': ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229'],
                'Open Router': ['openai/gpt-4-turbo', 'anthropic/claude-3-sonnet', 'meta-llama/llama-3-8b-instruct']
            }

            models = predefined_models.get(provider_name, [])
            return Response({
                'success': True,
                'models': models
            })

    except Exception as e:
        logger.error(f"Error in fetch_models: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch models'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def system_settings_view(request):
    """Handle system settings GET and POST requests"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        if request.method == 'GET':
            # Get current system settings
            system_settings = SystemSettings.get_current_settings()
            
            return Response({
                'success': True,
                'data': {
                    'system_name': system_settings.system_name,
                    'system_description': system_settings.system_description,
                    'timezone': system_settings.timezone,
                    'date_format': system_settings.date_format,
                    'session_timeout': system_settings.session_timeout,
                    'password_min_length': system_settings.password_min_length,
                    'require_special_chars': system_settings.require_special_chars,
                    'enable_two_factor': system_settings.enable_two_factor,
                    'enable_email_notifications': system_settings.enable_email_notifications,
                    'enable_job_alerts': system_settings.enable_job_alerts,
                    'enable_candidate_alerts': system_settings.enable_candidate_alerts,
                    'enable_interview_reminders': system_settings.enable_interview_reminders,
                    'data_retention_days': system_settings.data_retention_days,
                    'enable_data_backup': system_settings.enable_data_backup,
                    'backup_frequency': system_settings.backup_frequency,
                    'enable_workflow_automation': system_settings.enable_workflow_automation,
                    'default_workflow_timeout': system_settings.default_workflow_timeout,
                    'default_meeting_duration': system_settings.default_meeting_duration,
                    'working_hours_start': system_settings.working_hours_start.strftime('%H:%M'),
                    'working_hours_end': system_settings.working_hours_end.strftime('%H:%M'),
                }
            })

        elif request.method == 'POST':
            # Update system settings
            data = json.loads(request.body)
            system_settings = SystemSettings.get_current_settings()
            
            # Update fields
            for field, value in data.items():
                if hasattr(system_settings, field):
                    setattr(system_settings, field, value)
            
            system_settings.save()
            
            return Response({
                'success': True,
                'message': 'System settings saved successfully'
            })

    except Exception as e:
        logger.error(f"Error in system_settings_view: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to process system settings'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def fix_ollama_configuration(request):
    """Fix Ollama configuration by marking it as configured if it's working"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            ollama_config = AIProviderConfig.objects.get(provider_name='Ollama')

            # Test if Ollama is working
            if REQUESTS_AVAILABLE:
                import requests
                response = requests.get(f"{ollama_config.llm_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    ollama_config.is_configured = True
                    ollama_config.save()

                    # Also fetch and save available models
                    models_data = response.json()
                    models = [model['name'] for model in models_data.get('models', [])]
                    ollama_config.available_models = models
                    ollama_config.save()

                    return Response({
                        'success': True,
                        'message': 'Ollama configuration fixed successfully',
                        'available_models': models
                    })
                else:
                    return Response({
                        'success': False,
                        'error': f'Ollama connection failed: HTTP {response.status_code}'
                    })
            else:
                return Response({
                    'success': False,
                    'error': 'Requests module not available'
                })

        except AIProviderConfig.DoesNotExist:
            return Response({
                'success': False,
                'error': 'Ollama configuration not found'
            })

    except Exception as e:
        logger.error(f"Error fixing Ollama configuration: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fix Ollama configuration'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
