import asyncio
import threading
import time
from queue import Queue, Empty
from typing import Dict, Any, Callable, Optional
import logging

logger = logging.getLogger(__name__)


class LLMRequestQueue:
    """Simple queue system for LLM requests to prevent server overload"""
    
    def __init__(self, max_concurrent_requests: int = 3, request_timeout: int = 30):
        self.max_concurrent_requests = max_concurrent_requests
        self.request_timeout = request_timeout
        self.queue = Queue()
        self.active_requests = 0
        self.lock = threading.Lock()
        self.worker_thread = None
        self.running = False
        
    def start_worker(self):
        """Start the worker thread to process queued requests"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.running = True
            self.worker_thread = threading.Thread(target=self._worker, daemon=True)
            self.worker_thread.start()
            logger.info("LLM request queue worker started")
    
    def stop_worker(self):
        """Stop the worker thread"""
        self.running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
            logger.info("LLM request queue worker stopped")
    
    def _worker(self):
        """Worker thread that processes queued requests"""
        while self.running:
            try:
                # Check if we can process more requests
                with self.lock:
                    can_process = self.active_requests < self.max_concurrent_requests
                
                if can_process:
                    try:
                        # Get request from queue with timeout
                        request_data = self.queue.get(timeout=1)
                        
                        # Process the request in a separate thread
                        request_thread = threading.Thread(
                            target=self._process_request,
                            args=(request_data,),
                            daemon=True
                        )
                        request_thread.start()
                        
                    except Empty:
                        # No requests in queue, continue
                        continue
                else:
                    # Too many active requests, wait a bit
                    time.sleep(0.1)
                    
            except Exception as e:
                logger.error(f"Error in LLM queue worker: {str(e)}")
                time.sleep(1)
    
    def _process_request(self, request_data: Dict[str, Any]):
        """Process a single request"""
        request_id = request_data.get('id')
        callback = request_data.get('callback')
        args = request_data.get('args', ())
        kwargs = request_data.get('kwargs', {})
        
        try:
            # Increment active request count
            with self.lock:
                self.active_requests += 1
            
            logger.info(f"Processing LLM request {request_id}")
            
            # Execute the callback function
            if callback:
                result = callback(*args, **kwargs)
                logger.info(f"LLM request {request_id} completed successfully")
            else:
                logger.warning(f"No callback provided for request {request_id}")
                
        except Exception as e:
            logger.error(f"Error processing LLM request {request_id}: {str(e)}")
        finally:
            # Decrement active request count
            with self.lock:
                self.active_requests -= 1
            
            # Mark task as done
            self.queue.task_done()
    
    def add_request(self, request_id: str, callback: Callable, *args, **kwargs) -> bool:
        """Add a request to the queue"""
        try:
            request_data = {
                'id': request_id,
                'callback': callback,
                'args': args,
                'kwargs': kwargs,
                'timestamp': time.time()
            }
            
            self.queue.put(request_data)
            logger.info(f"Added LLM request {request_id} to queue")
            
            # Start worker if not running
            if not self.running:
                self.start_worker()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to add request {request_id} to queue: {str(e)}")
            return False
    
    def get_queue_status(self) -> Dict[str, Any]:
        """Get current queue status"""
        with self.lock:
            return {
                'queue_size': self.queue.qsize(),
                'active_requests': self.active_requests,
                'max_concurrent_requests': self.max_concurrent_requests,
                'worker_running': self.running
            }


# Global queue instance
_llm_queue = None


def get_llm_queue() -> LLMRequestQueue:
    """Get the global LLM queue instance"""
    global _llm_queue
    if _llm_queue is None:
        from .models import AISettings
        try:
            settings = AISettings.get_current_settings()
            _llm_queue = LLMRequestQueue(
                max_concurrent_requests=settings.max_concurrent_requests,
                request_timeout=settings.request_timeout
            )
        except Exception:
            # Fallback to default settings
            _llm_queue = LLMRequestQueue()
    
    return _llm_queue


def queue_llm_request(request_id: str, callback: Callable, *args, **kwargs) -> bool:
    """Queue an LLM request for processing"""
    queue = get_llm_queue()
    return queue.add_request(request_id, callback, *args, **kwargs)


def get_queue_status() -> Dict[str, Any]:
    """Get current queue status"""
    queue = get_llm_queue()
    return queue.get_queue_status()


# Example usage function for LLM requests
def make_llm_request(prompt: str, provider_config, **kwargs):
    """Make an LLM request to the specified provider"""
    try:
        print(f"=== MAKING LLM REQUEST TO {provider_config.provider_name} ===")
        logger.info(f"Starting LLM request to {provider_config.provider_name}")

        if not REQUESTS_AVAILABLE:
            print("ERROR: Requests module not available for LLM calls")
            logger.error("Requests module not available for LLM calls")
            return {
                'success': False,
                'error': 'Requests module not available'
            }

        import requests

        # Get provider configuration
        api_key = provider_config.get_api_key()
        url = provider_config.llm_url
        model = provider_config.selected_model

        logger.info(f"Provider: {provider_config.provider_name}")
        logger.info(f"URL: {url}")
        logger.info(f"Model: {model}")
        logger.info(f"Has API Key: {bool(api_key)}")

        # Validate required parameters
        if not model:
            logger.error("No model selected for LLM request")
            return {
                'success': False,
                'error': 'No model selected. Please select a model in AI settings.'
            }

        if provider_config.provider_name == 'Ollama':
            # Ollama API call
            print("Making Ollama API request...")
            logger.info("Making Ollama API request...")
            request_data = {
                "model": model,
                "prompt": prompt,
                "stream": False
            }
            print(f"Request data: model={model}, prompt_length={len(prompt)}")
            logger.info(f"Request data: {request_data}")

            print(f"Sending request to: {url}/api/generate")
            response = requests.post(
                f"{url}/api/generate",
                json=request_data,
                timeout=300  # Increased timeout to 5 minutes for complex evaluations
            )
            print(f"Ollama response status: {response.status_code}")
            logger.info(f"Ollama response status: {response.status_code}")
        elif provider_config.provider_name == 'Groq':
            # Groq API call
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            response = requests.post(
                f"{url}/chat/completions",
                headers=headers,
                json={
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}]
                },
                timeout=300  # Increased timeout to 5 minutes for complex evaluations
            )
        # Add other providers as needed
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ LLM request successful")
            print(f"Raw response data: {response_data}")
            logger.info(f"Raw response data: {response_data}")

            # Extract the actual response text based on provider
            if provider_config.provider_name == 'Ollama':
                # Ollama returns response in 'response' field
                actual_response = response_data.get('response', '')
                logger.info(f"Extracted Ollama response: {actual_response[:200]}...")
            elif provider_config.provider_name == 'Groq':
                # Groq returns response in choices[0].message.content
                choices = response_data.get('choices', [])
                if choices:
                    actual_response = choices[0].get('message', {}).get('content', '')
                else:
                    actual_response = ''
                logger.info(f"Extracted Groq response: {actual_response[:200]}...")
            else:
                # Default fallback
                actual_response = str(response_data)
                logger.info(f"Extracted default response: {actual_response[:200]}...")

            result = {
                'success': True,
                'response': actual_response
            }
            logger.info(f"Returning successful response with length: {len(actual_response)}")
            return result
        else:
            logger.error(f"LLM request failed: {response.status_code}")
            return {
                'success': False,
                'error': f'HTTP {response.status_code}'
            }
            
    except requests.exceptions.Timeout as e:
        logger.error(f"LLM request timed out after 5 minutes: {str(e)}")
        return {
            'success': False,
            'error': 'Request timed out. The AI model is taking too long to respond. Please try again or check if the model is overloaded.'
        }
    except requests.exceptions.ConnectionError as e:
        logger.error(f"Connection error to LLM provider: {str(e)}")
        return {
            'success': False,
            'error': 'Cannot connect to AI provider. Please check if the service is running and accessible.'
        }
    except Exception as e:
        logger.error(f"Error making LLM request: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


# Try to import requests for LLM calls
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
