from django.contrib import admin
from .models import AISettings, SystemSettings, AIProviderConfig


@admin.register(AIProviderConfig)
class AIProviderConfigAdmin(admin.ModelAdmin):
    list_display = ['provider_name', 'llm_url', 'selected_model', 'is_configured', 'updated_at']
    list_filter = ['provider_name', 'is_configured']
    readonly_fields = ['created_at', 'updated_at']

    def get_readonly_fields(self, request, obj=None):
        # Make encrypted_api_key readonly to prevent direct editing
        readonly = list(self.readonly_fields)
        readonly.append('encrypted_api_key')
        return readonly


@admin.register(AISettings)
class AISettingsAdmin(admin.ModelAdmin):
    list_display = ['active_provider', 'enable_request_queue', 'max_concurrent_requests', 'is_active', 'updated_at']
    list_filter = ['active_provider', 'enable_request_queue', 'is_active']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(SystemSettings)
class SystemSettingsAdmin(admin.ModelAdmin):
    list_display = ['system_name', 'timezone', 'is_active', 'updated_at']
    list_filter = ['is_active', 'timezone']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('General Settings', {
            'fields': ('system_name', 'system_description', 'timezone', 'date_format')
        }),
        ('Security Settings', {
            'fields': ('session_timeout', 'password_min_length', 'require_special_chars', 'enable_two_factor')
        }),
        ('Notification Settings', {
            'fields': ('enable_email_notifications', 'enable_job_alerts', 'enable_candidate_alerts', 'enable_interview_reminders')
        }),
        ('Data Management', {
            'fields': ('data_retention_days', 'enable_data_backup', 'backup_frequency')
        }),
        ('Workflow Settings', {
            'fields': ('enable_workflow_automation', 'default_workflow_timeout')
        }),
        ('Scheduling Settings', {
            'fields': ('default_meeting_duration', 'working_hours_start', 'working_hours_end')
        }),
        ('System Info', {
            'fields': ('is_active', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
