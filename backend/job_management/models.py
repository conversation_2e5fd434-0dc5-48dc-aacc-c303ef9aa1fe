from mongoengine import Document, StringField, IntField, DateTimeField, FileField, BooleanField, ListField, DictField, FloatField, ReferenceField
import datetime

class JobRole(Document):
    """
    Job Role model stored in MongoDB
    """
    role_name = StringField(required=True, max_length=100)
    years_of_experience = IntField(required=True, min_value=0)
    job_description = StringField(max_length=5000)
    job_description_file = FileField()  # For uploaded files (PDF, DOC, TXT)
    description_type = StringField(choices=['text', 'file'], default='text')  # Track input type
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    is_active = BooleanField(default=True)
    
    meta = {
        'collection': 'job_roles',
        'indexes': [
            'role_name',
            'years_of_experience',
            'is_active',
            {'fields': ['$role_name', '$job_description'],
             'default_language': 'english',
             'weights': {'role_name': 10, 'job_description': 5}
            }
        ]
    }
    
    def __str__(self):
        return f"{self.role_name} ({self.years_of_experience} years)"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(JobRole, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job role to dictionary for API responses"""
        return {
            'id': str(self.id),
            'role_name': self.role_name,
            'years_of_experience': self.years_of_experience,
            'job_description': self.job_description,
            'description_type': self.description_type,
            'has_file': bool(self.job_description_file),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
        }

class Job(Document):
    """
    Job model stored in MongoDB
    """
    ticket_id = StringField(max_length=50)
    job_title = StringField(required=True, max_length=200)
    job_role_id = StringField(required=True)  # Reference to JobRole
    date = StringField(max_length=20)  # Date as string
    recruiter = StringField(max_length=100)
    ta_incharge = StringField(max_length=100)
    client = StringField(max_length=100)
    interview_panel = StringField(max_length=500)
    sourcing_type = StringField(choices=['vendor', 'direct', 'internal', 'all'])
    priority = StringField(choices=['high', 'medium', 'low'])
    candidates_applied = IntField(default=0)
    candidates_interviewed = IntField(default=0)
    availability = StringField(choices=['vacant', 'hired'])
    vendor_id = StringField()  # Reference to Vendor when sourcing_type is 'vendor'

    # Job Description fields
    default_job_description = StringField(max_length=10000)  # From job role
    additional_job_description = StringField(max_length=10000)  # Additional info
    job_description_file = FileField()  # Uploaded file
    job_description_filename = StringField(max_length=255)  # Original filename

    is_active = BooleanField(default=True)
    is_published = BooleanField(default=True)  # Changed default to True
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the job
    
    meta = {
        'collection': 'jobs',
        'indexes': [
            'ticket_id',
            'job_title',
            'job_role_id',
            'recruiter',
            'client',
            'sourcing_type',
            'priority',
            'availability',
            'is_active',
            'is_published',
            'created_at'
        ]
    }
    
    def __str__(self):
        return f"{self.job_title} at {self.company_name}"
    
    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Job, self).save(*args, **kwargs)
    
    def to_dict(self):
        """Convert job to dictionary for API responses"""
        return {
            'id': str(self.id),
            'ticket_id': self.ticket_id,
            'job_title': self.job_title,
            'job_role_id': self.job_role_id,
            'date': self.date,
            'recruiter': self.recruiter,
            'ta_incharge': self.ta_incharge,
            'client': self.client,
            'interview_panel': self.interview_panel,
            'sourcing_type': self.sourcing_type,
            'priority': self.priority,
            'candidates_applied': self.candidates_applied,
            'candidates_interviewed': self.candidates_interviewed,
            'availability': self.availability,
            'vendor_id': self.vendor_id,
            'default_job_description': self.default_job_description,
            'additional_job_description': self.additional_job_description,
            'job_description_filename': self.job_description_filename,
            'is_active': self.is_active,
            'is_published': self.is_published,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
        }


class BulkEvaluationSession(Document):
    """
    Bulk evaluation session to track resume evaluation processes
    """
    job = ReferenceField(Job, required=True)
    user_id = StringField(required=True)  # User who created the session
    session_name = StringField(max_length=200)
    selected_criteria = ListField(StringField())  # List of criteria IDs
    status = StringField(choices=['pending', 'in_progress', 'completed', 'failed'], default='pending')
    total_resumes = IntField(default=0)
    processed_resumes = IntField(default=0)
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    completed_at = DateTimeField()
    error_message = StringField()

    meta = {
        'collection': 'bulk_evaluation_sessions',
        'indexes': [
            'job',
            'user_id',
            'status',
            'created_at'
        ]
    }

    def to_dict(self):
        return {
            'id': str(self.id),
            'job_id': str(self.job.id) if self.job else None,
            'user_id': self.user_id,
            'session_name': self.session_name,
            'selected_criteria': self.selected_criteria,
            'status': self.status,
            'total_resumes': self.total_resumes,
            'processed_resumes': self.processed_resumes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'error_message': self.error_message,
        }


class UploadedResume(Document):
    """
    Uploaded resume files for bulk evaluation
    """
    session = ReferenceField(BulkEvaluationSession, required=True)
    filename = StringField(required=True, max_length=255)
    original_filename = StringField(required=True, max_length=255)
    file_size = IntField()
    file_content = FileField()  # Store the actual file
    upload_order = IntField(default=0)
    status = StringField(choices=['uploaded', 'processing', 'completed', 'failed'], default='uploaded')
    visible = BooleanField(default=True)  # For soft delete functionality
    uploaded_at = DateTimeField(default=datetime.datetime.now)
    processed_at = DateTimeField()
    error_message = StringField()

    meta = {
        'collection': 'uploaded_resumes',
        'indexes': [
            'session',
            'status',
            'uploaded_at'
        ]
    }

    def to_dict(self):
        return {
            'id': str(self.id),
            'session_id': str(self.session.id) if self.session else None,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'upload_order': self.upload_order,
            'status': self.status,
            'visible': self.visible,
            'uploaded_at': self.uploaded_at.isoformat() if self.uploaded_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'error_message': self.error_message,
        }


class EvaluationResult(Document):
    """
    Results of resume evaluation
    """
    session = ReferenceField(BulkEvaluationSession, required=True)
    resume = ReferenceField(UploadedResume, required=True)
    scores = DictField()  # {criteria_id: score}
    comments = DictField()  # {criteria_id: comment}
    average_score = FloatField()
    rank = IntField()
    evaluation_data = DictField()  # Store additional evaluation metadata
    created_at = DateTimeField(default=datetime.datetime.now)

    meta = {
        'collection': 'evaluation_results',
        'indexes': [
            'session',
            'resume',
            'average_score',
            'rank'
        ]
    }

    def to_dict(self):
        return {
            'id': str(self.id),
            'session_id': str(self.session.id) if self.session else None,
            'resume_id': str(self.resume.id) if self.resume else None,
            'resumeId': str(self.resume.id) if self.resume else None,  # Frontend expects camelCase
            'resumeName': self.resume.original_filename if self.resume else None,  # Frontend expects camelCase
            'resume_name': self.resume.original_filename if self.resume else None,  # Keep for backward compatibility
            'scores': self.scores,
            'comments': self.comments,
            'averageScore': self.average_score,  # Frontend expects camelCase
            'average_score': self.average_score,  # Keep for backward compatibility
            'rank': self.rank,
            'evaluation_data': self.evaluation_data,
            'created_at': self.created_at.isoformat() if self.created_at else None,
        }
