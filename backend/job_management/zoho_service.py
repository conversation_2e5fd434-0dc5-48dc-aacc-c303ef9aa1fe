import os
import requests
import logging
from datetime import datetime
from django.conf import settings
from decouple import config, Config, RepositoryEnv

logger = logging.getLogger(__name__)

class ZohoAPIService:
    """Service to handle Zoho Recruit API integration"""
    
    def __init__(self):
        # Try multiple methods to load environment variables
        self.client_id = self._load_credential('client_id')
        self.client_secret = self._load_credential('client_secret')
        self.refresh_token = "**********************************************************************"
        self.access_token = None
        self.base_url = "https://recruit.zoho.in"

        # Debug logging with credential validation
        logger.info(f"🔧 ZohoAPIService initialized")
        logger.info(f"📋 Environment variables loaded:")
        logger.info(f"   Client ID: {self.client_id if self.client_id else '❌ NOT FOUND'}")
        logger.info(f"   Client Secret: {self.client_secret[:15]}..." if self.client_secret else "   Client Secret: ❌ NOT FOUND")
        logger.info(f"   Refresh Token: {self.refresh_token[:30]}...")

        # Validate credentials
        if not self.client_id:
            logger.error("❌ CLIENT_ID not found in environment variables!")
        if not self.client_secret:
            logger.error("❌ CLIENT_SECRET not found in environment variables!")

        # Show exact values for debugging (first time only)
        logger.info(f"🔍 DEBUGGING - Full Client ID: {self.client_id}")
        logger.info(f"🔍 DEBUGGING - Full Client Secret: {self.client_secret}")

    def _load_credential(self, key):
        """Try multiple methods to load credentials"""
        # Method 1: decouple config with explicit .env path
        try:
            from pathlib import Path
            env_path = Path(__file__).resolve().parent.parent / '.env'
            logger.info(f"🔍 Looking for .env file at: {env_path}")

            if env_path.exists():
                logger.info(f"✅ Found .env file at: {env_path}")
                env_config = Config(RepositoryEnv(str(env_path)))
                value = env_config(key, default=None)
                if value:
                    logger.info(f"✅ Loaded {key} via explicit .env path")
                    return value
            else:
                logger.warning(f"❌ .env file not found at: {env_path}")
        except Exception as e:
            logger.warning(f"Failed to load {key} via explicit .env path: {e}")

        # Method 2: decouple config (Django standard)
        try:
            value = config(key, default=None)
            if value:
                logger.info(f"✅ Loaded {key} via decouple config")
                return value
        except Exception as e:
            logger.warning(f"Failed to load {key} via decouple: {e}")

        # Method 3: os.getenv
        try:
            value = os.getenv(key)
            if value:
                logger.info(f"✅ Loaded {key} via os.getenv")
                return value
        except Exception as e:
            logger.warning(f"Failed to load {key} via os.getenv: {e}")

        # Method 4: Direct hardcoded values as fallback (temporary for testing)
        fallback_values = {
            'client_id': '1000.80P8NXCX0O1UE7SWVRXK2TW65ZI82C',
            'client_secret': 'e66233b30492f2696347e6b3fd2962eaf189715b38'
        }

        if key in fallback_values:
            logger.warning(f"⚠️ Using hardcoded fallback for {key}")
            return fallback_values[key]

        logger.error(f"❌ Failed to load {key} via any method")
        return None
    
    def get_access_token(self):
        """Get access token using refresh token"""
        try:
            logger.info("Attempting to get access token from Zoho...")
            
            url = "https://accounts.zoho.in/oauth/v2/token"
            params = {
                'refresh_token': self.refresh_token,
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'refresh_token',
                'scope': 'ZohoRecruit.modules.all'
            }
            
            logger.info(f"🌐 Making token request to: {url}")
            logger.info(f"📋 Request params (sanitized): {dict(params, client_secret='***', refresh_token='***')}")
            logger.info(f"🔍 DEBUGGING - Actual client_id being sent: {params['client_id']}")
            logger.info(f"🔍 DEBUGGING - Actual client_secret being sent: {params['client_secret'][:20]}...")
            
            response = requests.post(url, params=params)
            
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ TOKEN RESPONSE RECEIVED")
                logger.info(f"Response keys: {list(data.keys())}")
                logger.info(f"Token type: {data.get('token_type', 'N/A')}")
                logger.info(f"Scope: {data.get('scope', 'N/A')}")
                logger.info(f"API Domain: {data.get('api_domain', 'N/A')}")
                logger.info(f"Expires in: {data.get('expires_in', 'N/A')} seconds")

                self.access_token = data.get('access_token')
                if self.access_token:
                    logger.info(f"🎉 ACCESS TOKEN SUCCESSFULLY RETRIEVED!")
                    logger.info(f"Token (first 30 chars): {self.access_token[:30]}...")
                    logger.info(f"Full authorization header will be: Zoho-oauthtoken {self.access_token[:30]}...")
                    return True
                else:
                    logger.error("❌ No access token in response")
                    logger.error(f"Full response: {data}")
                    return False
            else:
                logger.error(f"Failed to get access token. Status: {response.status_code}")
                logger.error(f"Response text: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Exception while getting access token: {str(e)}")
            return False
    
    def fetch_job_openings(self):
        """Fetch job openings from Zoho Recruit API"""
        try:
            if not self.access_token:
                logger.info("No access token available, attempting to get one...")
                if not self.get_access_token():
                    logger.error("Failed to obtain access token")
                    return None
            
            logger.info("🔄 FETCHING JOB OPENINGS FROM ZOHO...")

            url = f"{self.base_url}/recruit/private/json/JobOpenings/getRecords"
            headers = {
                'Authorization': f'Zoho-oauthtoken {self.access_token}'
            }
            params = {
                'fromIndex': 0,
                'toIndex': 1000,
                'sortOrderString': 'asc'
            }

            logger.info(f"📡 API URL: {url}")
            logger.info(f"🔑 Authorization header: Zoho-oauthtoken {self.access_token[:30]}...")
            logger.info(f"📋 Request params: {params}")
            logger.info(f"🚀 Making API request...")
            
            response = requests.get(url, headers=headers, params=params)

            logger.info(f"📊 RESPONSE STATUS: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ SUCCESSFULLY FETCHED JOB OPENINGS!")
                logger.info(f"📋 Response structure: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                # Log job count and sample
                if isinstance(data, dict) and 'response' in data:
                    result = data.get('response', {}).get('result', {})
                    job_openings = result.get('JobOpenings', {})
                    rows = job_openings.get('row', [])

                    logger.info(f"🎯 FOUND {len(rows)} JOB OPENINGS")
                    if rows:
                        first_job = rows[0] if len(rows) > 0 else None
                        if first_job and 'FL' in first_job:
                            # Extract job title for logging
                            job_title = "Unknown"
                            for field in first_job['FL']:
                                if field['val'] == 'Posting Title':
                                    job_title = field.get('content', 'Unknown')
                                    break
                            logger.info(f"📝 Sample job: {job_title}")
                        logger.info(f"🔍 First job structure: {list(first_job.keys()) if first_job else 'None'}")

                return data
            else:
                logger.error(f"Failed to fetch job openings. Status: {response.status_code}")
                logger.error(f"Response text: {response.text}")
                
                # Try to refresh token if unauthorized
                if response.status_code == 401:
                    logger.info("Unauthorized, attempting to refresh token...")
                    if self.get_access_token():
                        logger.info("Token refreshed, retrying job fetch...")
                        return self.fetch_job_openings()
                
                return None
                
        except Exception as e:
            logger.error(f"Exception while fetching job openings: {str(e)}")
            return None
    
    def parse_job_data(self, zoho_job):
        """Parse Zoho job data to our job model format"""
        try:
            # Extract field-value pairs from Zoho format
            fields = {}
            if 'FL' in zoho_job:
                for field in zoho_job['FL']:
                    fields[field['val']] = field.get('content', '')

            # Debug: Log all available fields for first few jobs
            if len(fields) > 0:
                logger.info(f"Available Zoho fields: {list(fields.keys())}")

            job_title = fields.get('Posting Title', '')
            logger.info(f"Parsing job: {job_title}")

            # Handle numeric fields safely
            def safe_int(value, default=0):
                try:
                    return int(value) if value else default
                except (ValueError, TypeError):
                    return default

            # Map Zoho fields to our model with corrected field mappings
            parsed_job = {
                'ticket_id': fields.get('Jira Ticket Number', ''),  # Use Jira Ticket Number instead of Job Opening ID
                'job_title': job_title,
                'date': self._parse_date(fields.get('Date Opened', '')),
                'recruiter': fields.get('Assigned Recruiter', ''),  # Keep as is
                'ta_incharge': fields.get('Hiring Manager', ''),
                'client': fields.get('Bell Hiring Managers', ''),  # Use Bell Hiring Managers for client
                'interview_panel': '',  # Keep empty as requested
                'sourcing_type': self._determine_sourcing_type(fields),  # Map to valid values
                'priority': self._determine_priority(fields),
                'candidates_applied': safe_int(fields.get('No of Applications', 0)),
                'candidates_interviewed': safe_int(fields.get('No of Applications Hired', 0)),
                'availability': self._map_availability(fields.get('Job Opening Status', '')),
                'job_role_id': self._get_default_job_role(),  # Provide default job role
                'zoho_id': fields.get('JOBOPENINGID', ''),  # Store original Zoho ID
                'location': fields.get('Location', ''),
                'experience_required': fields.get('Work Experience', ''),
                'required_skills': fields.get('Required Skills', ''),
                'job_description': fields.get('Job Description', ''),
                'department': fields.get('Department Name', ''),
                'industry': fields.get('Industry', ''),
                'positions_count': safe_int(fields.get('Number of Positions', 1)),
            }

            # Validate required fields
            if not parsed_job['ticket_id'] or not parsed_job['job_title']:
                logger.warning(f"Missing required fields for job: {fields}")
                return None

            logger.info(f"Successfully parsed job: {parsed_job['ticket_id']} - {parsed_job['job_title']}")
            return parsed_job

        except Exception as e:
            logger.error(f"Error parsing job data: {str(e)}")
            logger.error(f"Job data: {zoho_job}")
            return None
    
    def _parse_date(self, date_str):
        """Parse Zoho date format to our format"""
        try:
            if date_str:
                # Zoho format: "2025-08-12"
                return date_str
            return datetime.now().strftime('%Y-%m-%d')
        except:
            return datetime.now().strftime('%Y-%m-%d')
    
    def _determine_priority(self, fields):
        """Determine job priority based on Zoho fields"""
        if fields.get('Is Hot Job Opening') == 'true':
            return 'high'  # Must be lowercase
        elif fields.get('Job Type') == 'Contract':
            return 'medium'
        else:
            return 'medium'  # Default
    
    def _map_availability(self, status):
        """Map Zoho job status to our availability"""
        # Our model only accepts 'vacant' or 'hired'
        status_mapping = {
            'Start': 'vacant',
            'In Progress': 'vacant',
            'On Hold': 'vacant',
            'Cancelled': 'hired',  # Assuming cancelled means position filled
            'Closed': 'hired',
        }
        return status_mapping.get(status, 'vacant')  # Default to vacant

    def _determine_sourcing_type(self, fields):
        """Determine sourcing type based on Zoho fields"""
        # Our model accepts: 'vendor', 'direct', 'internal', 'all'

        # Check if it's from a vendor/agency
        if fields.get('Assigned Recruiter') and 'vendor' in fields.get('Assigned Recruiter', '').lower():
            return 'vendor'

        # Check if it's internal based on department or other indicators
        department = fields.get('Department Name', '').lower()
        if 'internal' in department or 'hr' in department:
            return 'internal'

        # Check job type for direct hiring indicators
        job_type = fields.get('Job Type', '').lower()
        if job_type == 'permanent':
            return 'direct'

        # Default to direct for external job postings from Zoho
        return 'direct'

    def _determine_client(self, fields):
        """Determine client name from various Zoho fields"""
        # Try multiple fields that might contain client information
        client_fields = [
            'Client',
            'Business Unit English',
            'Department Name',
            'Industry'
        ]

        for field in client_fields:
            if fields.get(field):
                return fields[field]

        # Default fallback
        return 'Zoho Import'

    def _determine_interview_panel(self, fields):
        """Try to extract interview panel information from Zoho fields"""
        # Look for fields that might contain interviewer information
        panel_fields = [
            'Bell Hiring Managers',
            'Bell Hiring Director',
            'Bell Hiring VP',
            'Hiring Manager',
            'Created By',
            'Modified By'
        ]

        panel_members = []
        for field in panel_fields:
            if fields.get(field) and fields[field] not in panel_members:
                panel_members.append(fields[field])

        # Return comma-separated list of unique panel members
        return ', '.join(panel_members[:3])  # Limit to first 3 members

    def _get_default_job_role(self):
        """Get or create a default job role for Zoho imports"""
        try:
            from .models import JobRole

            # Try to find an existing "General" or "Other" job role
            default_roles = ['General', 'Other', 'Unspecified', 'Default']

            for role_name in default_roles:
                try:
                    role = JobRole.objects.get(role_name=role_name, is_active=True)
                    logger.info(f"Using existing default job role: {role_name}")
                    return str(role.id)
                except JobRole.DoesNotExist:
                    continue

            # If no default role exists, create one
            default_role = JobRole(
                role_name="General",
                job_description="Default role for Zoho imports",
                years_of_experience=0,
                is_active=True
            )
            default_role.save()
            logger.info(f"Created default job role: General")
            return str(default_role.id)

        except Exception as e:
            logger.error(f"Error getting default job role: {str(e)}")
            # Try to get any existing job role as fallback
            try:
                from .models import JobRole
                any_role = JobRole.objects.filter(is_active=True).first()
                if any_role:
                    logger.info(f"Using fallback job role: {any_role.role_name}")
                    return str(any_role.id)
            except Exception as fallback_error:
                logger.error(f"Fallback job role lookup failed: {str(fallback_error)}")

            # If all else fails, return a placeholder that we'll handle in the sync
            return "default"

    def fetch_candidates(self):
        """Fetch candidates from Zoho Recruit API"""
        try:
            if not self.access_token:
                logger.info("No access token available, attempting to get one...")
                if not self.get_access_token():
                    logger.error("Failed to obtain access token")
                    return None

            logger.info("🔄 FETCHING CANDIDATES FROM ZOHO...")

            url = f"{self.base_url}/recruit/v2/Candidates"
            headers = {
                'Authorization': f'Zoho-oauthtoken {self.access_token}'
            }
            params = {
                'page': 1,
                'per_page': 200,
                'sort_by': 'Created_Time',
                'sort_order': 'desc'
            }

            logger.info(f"📡 API URL: {url}")
            logger.info(f"🔑 Authorization header: Zoho-oauthtoken {self.access_token[:30]}...")
            logger.info(f"📋 Request params: {params}")
            logger.info(f"🚀 Making API request...")

            response = requests.get(url, headers=headers, params=params)

            logger.info(f"📊 RESPONSE STATUS: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ SUCCESSFULLY FETCHED CANDIDATES!")
                logger.info(f"📋 Response structure: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

                # Log candidate count and sample
                if isinstance(data, dict) and 'data' in data:
                    candidates = data.get('data', [])
                    logger.info(f"🎯 FOUND {len(candidates)} CANDIDATES")

                    if candidates:
                        first_candidate = candidates[0]
                        candidate_name = first_candidate.get('Full_Name', 'Unknown')
                        logger.info(f"📝 Sample candidate: {candidate_name}")
                        logger.info(f"🔍 First candidate structure: {list(first_candidate.keys()) if first_candidate else 'None'}")

                return data
            else:
                logger.error(f"Failed to fetch candidates. Status: {response.status_code}")
                logger.error(f"Response text: {response.text}")

                # Try to refresh token if unauthorized
                if response.status_code == 401:
                    logger.info("Unauthorized, attempting to refresh token...")
                    if self.get_access_token():
                        logger.info("Token refreshed, retrying candidate fetch...")
                        return self.fetch_candidates()

                return None

        except Exception as e:
            logger.error(f"Exception while fetching candidates: {str(e)}")
            return None

    def parse_candidate_data(self, zoho_candidate):
        """Parse Zoho candidate data to our candidate model format"""
        try:
            candidate_name = zoho_candidate.get('Full_Name', 'Unknown')
            logger.info(f"Parsing candidate: {candidate_name}")

            # Handle numeric fields safely
            def safe_str(value, default=''):
                try:
                    return str(value) if value else default
                except (ValueError, TypeError):
                    return default

            # Map Zoho fields to our candidate model
            parsed_candidate = {
                'full_name': zoho_candidate.get('Full_Name', ''),
                'phone_number': zoho_candidate.get('Mobile', '') or zoho_candidate.get('Mobile_number', ''),
                'email': zoho_candidate.get('Email', ''),
                'candidate_id': zoho_candidate.get('Candidate_ID', ''),
                'city': self._extract_city_from_location(zoho_candidate.get('Location_ID', '')),
                'application_type': self._determine_application_type(zoho_candidate),
                'total_experience': str(zoho_candidate.get('Experience_in_Years', '') or ''),
                'last_job_date': '',  # Leave empty as requested
                'comments': zoho_candidate.get('Remarks', '') or zoho_candidate.get('Additional_Info', ''),
                'status': self._map_candidate_status(zoho_candidate.get('Candidate_Status', '')),
                'source': 'bulk_upload',  # Since it's from Zoho sync
                'zoho_id': zoho_candidate.get('id', ''),  # Store original Zoho ID
                'current_employer': zoho_candidate.get('Current_Employer', ''),
                'current_job_title': zoho_candidate.get('Current_Job_Title', ''),
                'expected_salary': zoho_candidate.get('Expected_Salary', ''),
                'current_salary': zoho_candidate.get('Current_Salary', ''),
                'notice_period': zoho_candidate.get('Notice_Period_In_days', ''),
                'highest_qualification': zoho_candidate.get('Highest_Qualification_Held', ''),
                'preferred_location': zoho_candidate.get('Preferred_Job_Location', ''),
            }

            # Validate required fields
            if not parsed_candidate['full_name']:
                logger.warning(f"Missing required field 'full_name' for candidate: {zoho_candidate}")
                return None

            logger.info(f"Successfully parsed candidate: {parsed_candidate['candidate_id']} - {parsed_candidate['full_name']}")
            return parsed_candidate

        except Exception as e:
            logger.error(f"Error parsing candidate data: {str(e)}")
            logger.error(f"Candidate data: {zoho_candidate}")
            return None

    def _extract_city_from_location(self, location_id):
        """Extract city from Location_ID field like '703671 : Bengaluru'"""
        try:
            if location_id and ':' in location_id:
                return location_id.split(':')[1].strip()
            return location_id or ''
        except:
            return ''

    def _determine_application_type(self, candidate_data):
        """Determine application type based on Zoho candidate data"""
        source = candidate_data.get('Source', '').lower()
        origin = candidate_data.get('Origin', '').lower()

        # Map Zoho sources to our application types
        if 'referral' in source or 'employee' in source:
            return 'Employee Referral'
        elif 'vendor' in source or 'agency' in source:
            return 'Vendor'
        else:
            return 'Job Portal'  # Default for CareerSite and other sources

    def _map_candidate_status(self, zoho_status):
        """Map Zoho candidate status to our status"""
        status_mapping = {
            'New': 'Active',
            'Qualified': 'Active',
            'Unqualified': 'Rejected',
            'Contacted': 'Active',
            'Junk': 'Rejected',
            'Disqualified': 'Rejected',
            'Hired': 'Hired',
            'Rejected': 'Rejected',
        }
        return status_mapping.get(zoho_status, 'Active')  # Default to Active

    def _parse_date_short(self, date_string):
        """Parse Zoho date string and return short date format (YYYY-MM-DD)"""
        try:
            if not date_string:
                return ''

            # Parse the Zoho datetime format: "2025-08-13T22:22:55+05:30"
            from datetime import datetime

            # Remove timezone info for parsing
            if '+' in date_string:
                date_part = date_string.split('+')[0]
            elif 'T' in date_string:
                date_part = date_string.split('T')[0]
            else:
                date_part = date_string

            # Try to parse as date
            try:
                parsed_date = datetime.strptime(date_part, '%Y-%m-%d')
                return parsed_date.strftime('%Y-%m-%d')  # Return as YYYY-MM-DD (10 chars)
            except ValueError:
                # If that fails, try full datetime parsing
                if 'T' in date_string:
                    datetime_part = date_string.split('+')[0] if '+' in date_string else date_string
                    parsed_date = datetime.strptime(datetime_part, '%Y-%m-%dT%H:%M:%S')
                    return parsed_date.strftime('%Y-%m-%d')  # Return just the date part
                else:
                    return date_string[:10] if len(date_string) >= 10 else date_string

        except Exception as e:
            logger.warning(f"Error parsing date '{date_string}': {str(e)}")
            return ''  # Return empty string if parsing fails

    def find_matching_job_role(self, job_title, required_skills, experience):
        """Find matching job role based on job details"""
        try:
            from .models import JobRole

            job_roles = JobRole.objects.filter(is_active=True)

            # Create search terms from job title and skills
            search_terms = []
            if job_title:
                search_terms.extend(job_title.lower().split())
            if required_skills:
                search_terms.extend([skill.strip().lower() for skill in required_skills.split(',') if skill.strip()])

            # Score each job role
            best_match = None
            best_score = 0

            for role in job_roles:
                score = 0
                role_name_lower = role.role_name.lower()

                # Check for exact matches in role name
                for term in search_terms:
                    if term in role_name_lower:
                        score += 2
                    elif any(word in role_name_lower for word in term.split()):
                        score += 1

                # Bonus for experience match
                if experience and hasattr(role, 'years_of_experience'):
                    try:
                        exp_years = int(''.join(filter(str.isdigit, experience)))
                        role_years = role.years_of_experience
                        if abs(exp_years - role_years) <= 2:
                            score += 1
                    except:
                        pass

                if score > best_score:
                    best_score = score
                    best_match = role

            if best_match and best_score >= 2:  # Minimum threshold
                logger.info(f"Matched job role: {best_match.role_name} (score: {best_score})")
                return str(best_match.id)

            logger.info(f"No suitable job role match found for: {job_title}")
            return None

        except Exception as e:
            logger.error(f"Error finding job role match: {str(e)}")
            return None
