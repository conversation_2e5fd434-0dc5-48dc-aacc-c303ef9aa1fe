import json
import logging
import os
import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework import status
from mongoengine.errors import ValidationError, DoesNotExist
from bson import ObjectId
from bson.errors import InvalidId

from accounts.models import User
from admin_controls.utils import check_admin_permission
from .models import JobRole, Job, BulkEvaluationSession, UploadedResume, EvaluationResult
from .evaluation_tasks import task_manager
from .llm_utils import evaluate_resume_with_llm

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_job_roles(request):
    """List all job roles with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = JobRole.objects.filter(is_active=True)

        # Apply search filter
        if search:
            queryset = queryset.filter(role_name__icontains=search)

        # Calculate pagination
        total_count = queryset.count()
        total_pages = (total_count + per_page - 1) // per_page
        skip = (page - 1) * per_page

        # Get job roles for current page
        job_roles = queryset.skip(skip).limit(per_page)

        # Prepare job role data
        job_role_data = [role.to_dict() for role in job_roles]

        return Response({
            'success': True,
            'data': {
                'job_roles': job_role_data,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'has_next': page < total_pages,
                    'has_previous': page > 1,
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_job_roles: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch job roles'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_job_role(request):
    """Create a new job role"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        role_name = data.get('role_name')
        years_of_experience = data.get('years_of_experience')
        job_description = data.get('job_description', '')
        description_type = data.get('description_type', 'text')

        if not role_name:
            return Response({
                'success': False,
                'error': 'Role name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if years_of_experience is None:
            return Response({
                'success': False,
                'error': 'Years of experience is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            years_of_experience = int(years_of_experience)
            if years_of_experience < 0:
                raise ValueError("Years of experience cannot be negative")
        except (ValueError, TypeError):
            return Response({
                'success': False,
                'error': 'Years of experience must be a valid non-negative number'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Check if job role already exists
        if JobRole.objects.filter(role_name=role_name, is_active=True).first():
            return Response({
                'success': False,
                'error': 'Job role with this name already exists'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create job role
        job_role = JobRole(
            role_name=role_name,
            years_of_experience=years_of_experience,
            job_description=job_description,
            description_type=description_type
        )
        job_role.save()

        logger.info(f"Job role created: {job_role.role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job role created successfully',
            'job_role': job_role.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_job_role_details(request, role_id):
    """Get detailed information about a job role"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'success': True,
            'data': job_role.to_dict()
        })

    except Exception as e:
        logger.error(f"Error in get_job_role_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch job role details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_job_role(request, role_id):
    """Update job role information"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update allowed fields
        if 'role_name' in data:
            # Check if another role with this name exists
            existing_role = JobRole.objects.filter(
                role_name=data['role_name'],
                is_active=True,
                id__ne=role_id
            ).first()
            
            if existing_role:
                return Response({
                    'success': False,
                    'error': 'Job role with this name already exists'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            job_role.role_name = data['role_name']

        if 'years_of_experience' in data:
            try:
                years = int(data['years_of_experience'])
                if years < 0:
                    raise ValueError("Years of experience cannot be negative")
                job_role.years_of_experience = years
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'error': 'Years of experience must be a valid non-negative number'
                }, status=status.HTTP_400_BAD_REQUEST)

        if 'job_description' in data:
            job_role.job_description = data['job_description']

        if 'description_type' in data:
            job_role.description_type = data['description_type']

        job_role.save()

        # Update default job description for all jobs using this role
        if 'job_description' in data:
            jobs_to_update = Job.objects.filter(job_role_id=str(job_role.id), is_active=True)
            for job in jobs_to_update:
                job.default_job_description = job_role.job_description
                job.save()

        logger.info(f"Job role updated: {job_role.role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job role updated successfully',
            'job_role': job_role.to_dict()
        })

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def delete_job_role(request, role_id):
    """Delete a job role (soft delete)"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job_role = JobRole.objects.get(id=role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job role not found'
            }, status=status.HTTP_404_NOT_FOUND)

        role_name = job_role.role_name
        job_role.is_active = False
        job_role.save()

        logger.info(f"Job role deleted: {role_name} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Job role {role_name} deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error in delete_job_role: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to delete job role'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_jobs(request):
    """List all jobs with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query with ordering (newest first)
        queryset = Job.objects.filter(is_active=True).order_by('-date')

        # Apply search filter
        if search:
            from mongoengine import Q
            queryset = queryset.filter(
                Q(job_title__icontains=search) | Q(ticket_id__icontains=search)
            )

        # Calculate pagination
        total_count = queryset.count()
        total_pages = (total_count + per_page - 1) // per_page
        skip = (page - 1) * per_page

        # Get jobs for current page
        jobs = queryset.skip(skip).limit(per_page)

        # Prepare job data with role information
        job_data = []
        for job in jobs:
            job_dict = job.to_dict()
            
            # Get job role details
            try:
                job_role = JobRole.objects.get(id=job.job_role_id, is_active=True)
                job_dict['job_role'] = {
                    'id': str(job_role.id),
                    'role_name': job_role.role_name,
                    'years_of_experience': job_role.years_of_experience
                }
            except DoesNotExist:
                job_dict['job_role'] = None
            
            job_data.append(job_dict)

        return Response({
            'success': True,
            'data': {
                'jobs': job_data,
                'pagination': {
                    'current_page': page,
                    'total_pages': total_pages,
                    'total_count': total_count,
                    'has_next': page < total_pages,
                    'has_previous': page > 1,
                }
            }
        })

    except Exception as e:
        logger.error(f"Error in list_jobs: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch jobs'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_job(request):
    """Create a new job"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        data = request.data
        job_role_id = data.get('job_role_id')
        ticket_id = data.get('ticket_id')
        date = data.get('date')
        recruiter = data.get('recruiter')
        ta_incharge = data.get('ta_incharge')
        client = data.get('client')
        interview_panel = data.get('interview_panel')
        sourcing_type = data.get('sourcing_type')
        priority = data.get('priority')
        candidates_applied = data.get('candidates_applied', 0)
        candidates_interviewed = data.get('candidates_interviewed', 0)
        availability = data.get('availability')
        vendor_id = data.get('vendor_id')

        if not job_role_id:
            return Response({
                'success': False,
                'error': 'Job role is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify job role exists
        try:
            job_role = JobRole.objects.get(id=job_role_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Invalid job role selected'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create job
        job = Job(
            ticket_id=ticket_id,
            job_role_id=job_role_id,
            job_title=job_role.role_name,  # Use role name as job title
            date=date,
            recruiter=recruiter,
            ta_incharge=ta_incharge,
            client=client,
            interview_panel=interview_panel,
            sourcing_type=sourcing_type,
            priority=priority,
            candidates_applied=candidates_applied,
            candidates_interviewed=candidates_interviewed,
            availability=availability,
            vendor_id=vendor_id,
            created_by=str(request.user.id)
        )
        job.save()

        logger.info(f"Job created: {job.job_title} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job created successfully',
            'job': job.to_dict()
        }, status=status.HTTP_201_CREATED)

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in create_job: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to create job'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def delete_job(request, job_id):
    """Delete a job"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job = Job.objects.get(id=job_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)

        job_title = job.job_title
        job.is_active = False  # Soft delete
        job.save()

        logger.info(f"Job deleted: {job_title} by {request.user.email}")

        return Response({
            'success': True,
            'message': f'Job "{job_title}" deleted successfully'
        })

    except Exception as e:
        logger.error(f"Error in delete_job: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to delete job'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_job_details(request, job_id):
    """Get job details by ID"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job = Job.objects.get(id=job_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Get job data
        job_data = job.to_dict()

        # Always sync default job description from job role
        if job.job_role_id:
            try:
                job_role = JobRole.objects.get(id=job.job_role_id, is_active=True)
                current_role_description = job_role.job_description or ''

                # Update job's default description if it differs from role description
                if job.default_job_description != current_role_description:
                    job.default_job_description = current_role_description
                    job.save()

                job_data['default_job_description'] = job.default_job_description
            except DoesNotExist:
                job_data['default_job_description'] = job.default_job_description or ''
        else:
            job_data['default_job_description'] = job.default_job_description or ''

        return Response({
            'success': True,
            'data': job_data
        })

    except Exception as e:
        logger.error(f"Error in get_job_details: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch job details'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_job(request, job_id):
    """Update job details"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        try:
            job = Job.objects.get(id=job_id, is_active=True)
        except (DoesNotExist, InvalidId):
            return Response({
                'success': False,
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)

        data = request.data

        # Update job fields
        if 'ticket_id' in data:
            job.ticket_id = data['ticket_id'].strip()
        if 'job_title' in data:
            job.job_title = data['job_title'].strip()
        if 'job_role_id' in data:
            job.job_role_id = data['job_role_id']
        if 'date' in data:
            job.date = data['date']
        if 'recruiter' in data:
            job.recruiter = data['recruiter'].strip()
        if 'ta_incharge' in data:
            job.ta_incharge = data['ta_incharge'].strip()
        if 'client' in data:
            job.client = data['client'].strip()
        if 'interview_panel' in data:
            job.interview_panel = data['interview_panel'].strip()
        if 'sourcing_type' in data:
            valid_sourcing_types = ['vendor', 'direct', 'internal', 'all']
            if data['sourcing_type'] in valid_sourcing_types:
                job.sourcing_type = data['sourcing_type']
        if 'priority' in data:
            valid_priorities = ['high', 'medium', 'low']
            if data['priority'] in valid_priorities:
                job.priority = data['priority']
        if 'candidates_applied' in data:
            job.candidates_applied = int(data['candidates_applied']) if data['candidates_applied'] else 0
        if 'candidates_interviewed' in data:
            job.candidates_interviewed = int(data['candidates_interviewed']) if data['candidates_interviewed'] else 0
        if 'availability' in data:
            valid_availability = ['vacant', 'hired']
            if data['availability'] in valid_availability:
                job.availability = data['availability']

        job.save()

        logger.info(f"Job updated: {job.job_title} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Job updated successfully',
            'data': job.to_dict()
        })

    except ValidationError as e:
        return Response({
            'success': False,
            'error': f'Validation error: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"Error in update_job: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update job'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def sync_jobs_from_zoho(request):
    """Sync jobs from Zoho Recruit API"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        from .zoho_service import ZohoAPIService

        logger.info(f"Starting Zoho job sync requested by {request.user.email}")

        # Initialize Zoho service
        zoho_service = ZohoAPIService()

        # Fetch jobs from Zoho
        zoho_data = zoho_service.fetch_job_openings()

        if not zoho_data:
            logger.error("Failed to fetch data from Zoho API")
            return Response({
                'success': False,
                'error': 'Failed to fetch data from Zoho API'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Parse the response
        try:
            result = zoho_data.get('response', {}).get('result', {})
            job_openings = result.get('JobOpenings', {})
            rows = job_openings.get('row', [])

            logger.info(f"Processing {len(rows)} jobs from Zoho")

            created_count = 0
            updated_count = 0
            skipped_count = 0
            errors = []

            for zoho_job in rows:
                try:
                    # Parse job data
                    parsed_job = zoho_service.parse_job_data(zoho_job)

                    if not parsed_job:
                        skipped_count += 1
                        continue

                    # Check if job already exists (try both ticket_id and zoho_id)
                    existing_job = None
                    try:
                        # First try by ticket_id (primary identifier)
                        existing_job = Job.objects.get(ticket_id=parsed_job['ticket_id'])
                        logger.info(f"Found existing job by ticket_id: {parsed_job['ticket_id']}")
                    except Job.DoesNotExist:
                        # If not found by ticket_id, try by zoho_id (fallback)
                        if parsed_job.get('zoho_id'):
                            try:
                                # Note: This would require adding zoho_id field to Job model
                                # For now, we'll rely on ticket_id only
                                pass
                            except:
                                pass
                        logger.info(f"New job: {parsed_job['ticket_id']}")

                    # Try to find matching job role using improved matching
                    matched_job_role_id = zoho_service.find_matching_job_role(
                        parsed_job.get('job_title', ''),
                        parsed_job.get('required_skills', ''),
                        parsed_job.get('experience_required', '')
                    )

                    # Use matched role if found, otherwise use the default from parsed_job
                    job_role_id = matched_job_role_id if matched_job_role_id else parsed_job.get('job_role_id')

                    # Ensure we have a valid job_role_id
                    if not job_role_id or job_role_id == "default":
                        job_role_id = zoho_service._get_default_job_role()
                        if not job_role_id or job_role_id == "default":
                            logger.error(f"Cannot create job without valid job_role_id: {parsed_job['ticket_id']}")
                            continue

                    # Create or update job
                    if existing_job:
                        # Update existing job
                        existing_job.job_title = parsed_job['job_title']
                        existing_job.date = parsed_job['date']
                        existing_job.recruiter = parsed_job['recruiter']
                        existing_job.ta_incharge = parsed_job['ta_incharge']
                        existing_job.client = parsed_job['client']
                        existing_job.sourcing_type = parsed_job['sourcing_type']
                        existing_job.priority = parsed_job['priority']
                        existing_job.candidates_applied = parsed_job['candidates_applied']
                        existing_job.candidates_interviewed = parsed_job['candidates_interviewed']
                        existing_job.availability = parsed_job['availability']
                        existing_job.job_role_id = job_role_id

                        existing_job.save()
                        updated_count += 1
                        logger.info(f"Updated job: {parsed_job['ticket_id']}")

                    else:
                        # Create new job
                        new_job = Job(
                            ticket_id=parsed_job['ticket_id'],
                            job_title=parsed_job['job_title'],
                            job_role_id=job_role_id,
                            date=parsed_job['date'],
                            recruiter=parsed_job['recruiter'],
                            ta_incharge=parsed_job['ta_incharge'],
                            client=parsed_job['client'],
                            interview_panel=parsed_job['interview_panel'],
                            sourcing_type=parsed_job['sourcing_type'],
                            priority=parsed_job['priority'],
                            candidates_applied=parsed_job['candidates_applied'],
                            candidates_interviewed=parsed_job['candidates_interviewed'],
                            availability=parsed_job['availability'],
                            created_by=str(request.user.id)
                        )

                        new_job.save()
                        created_count += 1
                        logger.info(f"Created job: {parsed_job['ticket_id']}")

                except Exception as e:
                    error_msg = f"Error processing job: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
                    skipped_count += 1

            logger.info(f"Zoho sync completed. Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}")

            return Response({
                'success': True,
                'message': f'Sync completed successfully',
                'data': {
                    'created': created_count,
                    'updated': updated_count,
                    'skipped': skipped_count,
                    'total_processed': len(rows),
                    'errors': errors[:5]  # Limit error messages
                }
            })

        except Exception as e:
            logger.error(f"Error processing Zoho response: {str(e)}")
            return Response({
                'success': False,
                'error': f'Error processing Zoho response: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        logger.error(f"Error in sync_jobs_from_zoho: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to sync jobs from Zoho'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_jobs_over_time(request):
    """Get jobs count over time for dashboard chart"""
    try:
        from datetime import datetime, timedelta
        from collections import defaultdict

        # Get date range from query params (default to last 30 days)
        days = int(request.GET.get('days', 30))
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        # Fetch all active jobs (we'll filter by date field instead of created_at)
        jobs = Job.objects.filter(is_active=True)

        # Group jobs by date (using the date field from jobs)
        jobs_by_date = defaultdict(int)

        for job in jobs:
            # Use the job's date field (which is already in YYYY-MM-DD format)
            if job.date:
                try:
                    # Parse the date to ensure it's in the date range
                    from datetime import datetime
                    job_date = datetime.strptime(job.date, '%Y-%m-%d').date()
                    if start_date.date() <= job_date <= end_date.date():
                        jobs_by_date[job.date] += 1
                except ValueError:
                    # Skip jobs with invalid date format
                    continue

        # Create complete date range with zero counts for missing dates
        current_date = start_date
        chart_data = []

        while current_date <= end_date:
            date_str = current_date.strftime('%Y-%m-%d')
            chart_data.append({
                'date': date_str,
                'count': jobs_by_date.get(date_str, 0)
            })
            current_date += timedelta(days=1)

        return Response({
            'success': True,
            'data': chart_data,
            'total_jobs': sum(jobs_by_date.values()),
            'date_range': {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d'),
                'days': days
            }
        })

    except Exception as e:
        logger.error(f"Error getting jobs over time: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get jobs over time data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_jobs_by_title(request):
    """Get job counts by job title for bar chart"""
    try:
        from collections import defaultdict

        # Fetch all active jobs
        jobs = Job.objects.filter(is_active=True)

        # Group jobs by title
        jobs_by_title = defaultdict(int)

        for job in jobs:
            title = job.job_title or 'Untitled'
            jobs_by_title[title] += 1

        # Convert to list of dictionaries and sort by count (descending)
        chart_data = [
            {'title': title, 'count': count}
            for title, count in jobs_by_title.items()
        ]
        chart_data.sort(key=lambda x: x['count'], reverse=True)

        # Limit to top 10 job titles for better visualization
        chart_data = chart_data[:10]

        return Response({
            'success': True,
            'data': chart_data,
            'total_unique_titles': len(jobs_by_title),
            'total_jobs': sum(jobs_by_title.values())
        })

    except Exception as e:
        logger.error(f"Error getting jobs by title: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to get jobs by title data'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def test_zoho_credentials(request):
    """Test Zoho API credentials"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        from .zoho_service import ZohoAPIService
        from decouple import config
        import os

        # Check environment variables using both methods
        client_id_decouple = config('client_id', default=None)
        client_secret_decouple = config('client_secret', default=None)
        client_id_os = os.getenv('client_id')
        client_secret_os = os.getenv('client_secret')

        logger.info(f"Decouple config - Client ID: {client_id_decouple}")
        logger.info(f"Decouple config - Client Secret: {client_secret_decouple[:20]}..." if client_secret_decouple else "None")
        logger.info(f"OS getenv - Client ID: {client_id_os}")
        logger.info(f"OS getenv - Client Secret: {client_secret_os[:20]}..." if client_secret_os else "None")

        # Test token acquisition
        zoho_service = ZohoAPIService()
        token_success = zoho_service.get_access_token()

        return Response({
            'success': True,
            'data': {
                'client_id_decouple_loaded': bool(client_id_decouple),
                'client_secret_decouple_loaded': bool(client_secret_decouple),
                'client_id_os_loaded': bool(client_id_os),
                'client_secret_os_loaded': bool(client_secret_os),
                'client_id_preview': client_id_decouple[:20] + "..." if client_id_decouple else None,
                'client_secret_preview': client_secret_decouple[:10] + "..." if client_secret_decouple else None,
                'token_acquisition_success': token_success,
                'access_token_preview': zoho_service.access_token[:20] + "..." if zoho_service.access_token else None
            }
        })

    except Exception as e:
        logger.error(f"Error testing Zoho credentials: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_job_description(request, job_id):
    """Update job description fields and handle file uploads"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        # Get the job
        try:
            job = Job.objects.get(id=job_id, is_active=True)
        except DoesNotExist:
            return Response({
                'success': False,
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Handle file upload
        if 'job_description_file' in request.FILES:
            uploaded_file = request.FILES['job_description_file']

            # Validate file type
            allowed_extensions = ['.pdf', '.doc', '.docx', '.txt']
            file_extension = os.path.splitext(uploaded_file.name)[1].lower()

            if file_extension not in allowed_extensions:
                return Response({
                    'success': False,
                    'error': 'Invalid file type. Allowed: PDF, DOC, DOCX, TXT'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Save file
            job.job_description_file = uploaded_file
            job.job_description_filename = uploaded_file.name

        # Update text fields
        if 'additional_job_description' in request.POST:
            job.additional_job_description = request.POST.get('additional_job_description', '')

        job.save()

        return Response({
            'success': True,
            'message': 'Job description updated successfully',
            'data': {
                'additional_job_description': job.additional_job_description,
                'job_description_filename': job.job_description_filename
            }
        })

    except Exception as e:
        logger.error(f"Error updating job description: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to update job description'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def evaluate_resumes(request):
    """Evaluate resumes against job description using LLM"""
    try:
        logger.info("=== STARTING RESUME EVALUATION ===")

        # Check if required libraries are available
        try:
            import PyPDF2
            logger.info("PyPDF2 library available")
        except ImportError:
            logger.warning("PyPDF2 library not installed - PDF processing will be limited")

        try:
            from docx import Document
            logger.info("python-docx library available")
        except ImportError:
            logger.warning("python-docx library not installed - Word document processing will be limited")

        if not check_admin_permission(request.user):
            logger.error("Permission denied for user")
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        job_id = request.POST.get('job_id')
        criteria_json = request.POST.get('criteria')
        is_full_evaluation = request.POST.get('is_full_evaluation', 'true').lower() == 'true'

        logger.info(f"Request parameters - Job ID: {job_id}, Criteria: {criteria_json}, Full Evaluation: {is_full_evaluation}")
        logger.info(f"Files received: {list(request.FILES.keys())}")

        if not job_id or not criteria_json:
            logger.error("Missing required parameters: job_id or criteria")
            return Response({
                'success': False,
                'error': 'Job ID and criteria are required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get job details
        try:
            logger.info(f"Fetching job with ID: {job_id}")
            job = Job.objects.get(id=job_id, is_active=True)
            logger.info(f"Job found: {job.job_title}")
        except DoesNotExist:
            logger.error(f"Job not found with ID: {job_id}")
            return Response({
                'success': False,
                'error': 'Job not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Parse criteria
        try:
            logger.info(f"Parsing criteria JSON: {criteria_json}")
            criteria_ids = json.loads(criteria_json)
            logger.info(f"Parsed criteria IDs: {criteria_ids}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse criteria JSON: {str(e)}")
            return Response({
                'success': False,
                'error': 'Invalid criteria format'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get uploaded resume files
        logger.info("Processing uploaded files...")
        resume_files = []
        for key, file in request.FILES.items():
            if key.startswith('resume_'):
                logger.info(f"Found resume file: {file.name} (size: {file.size} bytes)")
                resume_files.append(file)

        logger.info(f"Total resume files found: {len(resume_files)}")
        if not resume_files:
            logger.error("No resume files uploaded")
            return Response({
                'success': False,
                'error': 'No resume files uploaded'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Build job description
        logger.info("Building job description...")
        job_description = ""
        if job.default_job_description:
            logger.info(f"Adding default job description (length: {len(job.default_job_description)})")
            job_description += f"Default Job Description:\n{job.default_job_description}\n\n"
        if job.additional_job_description:
            logger.info(f"Adding additional job description (length: {len(job.additional_job_description)})")
            job_description += f"Additional Requirements:\n{job.additional_job_description}\n\n"

        logger.info(f"Final job description length: {len(job_description)}")
        if not job_description.strip():
            logger.error("No job description available for evaluation")
            return Response({
                'success': False,
                'error': 'No job description available for evaluation'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Map criteria IDs to names
        criteria_map = {
            'skills_match': 'Skills Match',
            'experience_relevance': 'Experience Relevance',
            'education_fit': 'Education Fit',
            'technical_proficiency': 'Technical Proficiency',
            'communication_skills': 'Communication Skills',
            'leadership_experience': 'Leadership Experience',
            'industry_knowledge': 'Industry Knowledge',
            'cultural_fit': 'Cultural Fit',
            'career_progression': 'Career Progression',
            'certifications_training': 'Certifications & Training'
        }

        # Log evaluation type
        evaluation_type = "Full" if is_full_evaluation else "Incremental"
        logger.info(f"Starting {evaluation_type} evaluation for {len(resume_files)} resumes")

        # Process each resume
        results = []
        for i, resume_file in enumerate(resume_files):
            try:
                logger.info(f"Processing resume {i+1}/{len(resume_files)}: {resume_file.name}")

                # Extract text from resume (simplified - in production, use proper libraries)
                logger.info(f"Extracting text from {resume_file.name}...")
                resume_text = extract_text_from_file(resume_file)

                if not resume_text:
                    logger.warning(f"No text extracted from {resume_file.name}, skipping...")
                    continue

                logger.info(f"Extracted text length: {len(resume_text)} characters")

                # Evaluate resume using LLM
                try:
                    logger.info(f"Starting LLM evaluation for {resume_file.name}...")
                    evaluation_result = evaluate_resume_with_llm(
                        resume_text,
                        job_description,
                        criteria_ids,
                        criteria_map
                    )

                    # Handle both old format (just scores) and new format (scores + comments)
                    if isinstance(evaluation_result, dict) and 'scores' in evaluation_result:
                        scores = evaluation_result['scores']
                        comments = evaluation_result.get('comments', {})
                    else:
                        # Backward compatibility - old format was just scores
                        scores = evaluation_result
                        comments = {}

                    # Calculate average score
                    average_score = sum(scores.values()) / len(scores) if scores else 0
                    logger.info(f"LLM evaluation completed for {resume_file.name}. Average score: {average_score:.2f}")

                    results.append({
                        'resumeId': f'resume_{i}',
                        'resumeName': resume_file.name,
                        'scores': scores,
                        'comments': comments,
                        'averageScore': average_score
                    })
                except Exception as llm_error:
                    logger.error(f"LLM evaluation failed for {resume_file.name}: {str(llm_error)}")
                    # If this is the first resume and LLM fails, return error immediately
                    if i == 0:
                        logger.error("First resume evaluation failed, stopping process")
                        return Response({
                            'success': False,
                            'error': f'AI Evaluation Error: {str(llm_error)}'
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    else:
                        # For subsequent resumes, log and continue
                        logger.warning(f"Continuing with remaining resumes after failure on {resume_file.name}")
                        continue

            except Exception as e:
                logger.error(f"Error processing resume {resume_file.name}: {str(e)}")
                continue

        # Sort results by average score (highest first)
        results.sort(key=lambda x: x['averageScore'], reverse=True)

        # Add rank
        for i, result in enumerate(results):
            result['rank'] = i + 1

        logger.info(f"Evaluation completed successfully. Total results: {len(results)}")
        logger.info("=== RESUME EVALUATION COMPLETED ===")

        return Response({
            'success': True,
            'results': results,
            'message': f'Evaluated {len(results)} resumes successfully'
        })

    except Exception as e:
        logger.error(f"Critical error in evaluate_resumes: {str(e)}")
        logger.error("=== RESUME EVALUATION FAILED ===")
        return Response({
            'success': False,
            'error': 'Failed to evaluate resumes'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def extract_text_from_file(file):
    """Extract text from uploaded file"""
    try:
        import os
        from io import BytesIO

        # Get file extension
        file_extension = os.path.splitext(file.name)[1].lower()
        logger.info(f"Processing file: {file.name} (extension: {file_extension}, size: {file.size} bytes)")

        # Read file content
        file_content = file.read()
        file.seek(0)  # Reset file pointer
        logger.info(f"File content read: {len(file_content)} bytes")

        if file_extension == '.pdf':
            try:
                logger.info("Processing PDF file...")
                import PyPDF2
                pdf_reader = PyPDF2.PdfReader(BytesIO(file_content))
                text = ""
                logger.info(f"PDF has {len(pdf_reader.pages)} pages")
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    text += page_text + "\n"
                    logger.info(f"Extracted {len(page_text)} characters from page {page_num + 1}")
                final_text = text.strip()
                logger.info(f"Total PDF text extracted: {len(final_text)} characters")
                return final_text
            except ImportError:
                logger.warning("PyPDF2 not installed, using placeholder text")
                return f"PDF content from {file.name} (PyPDF2 required for extraction)"
            except Exception as e:
                logger.error(f"Error processing PDF {file.name}: {str(e)}")
                return f"Error processing PDF {file.name}: {str(e)}"

        elif file_extension in ['.doc', '.docx']:
            try:
                logger.info("Processing Word document...")
                from docx import Document
                doc = Document(BytesIO(file_content))
                text = ""
                logger.info(f"Document has {len(doc.paragraphs)} paragraphs")
                for paragraph in doc.paragraphs:
                    text += paragraph.text + "\n"
                final_text = text.strip()
                logger.info(f"Total Word document text extracted: {len(final_text)} characters")
                return final_text
            except ImportError:
                logger.warning("python-docx not installed, using placeholder text")
                return f"Word document content from {file.name} (python-docx required for extraction)"
            except Exception as e:
                logger.error(f"Error processing Word document {file.name}: {str(e)}")
                return f"Error processing Word document {file.name}: {str(e)}"

        elif file_extension == '.txt':
            try:
                logger.info("Processing text file...")
                text = file_content.decode('utf-8')
                logger.info(f"Text file content extracted: {len(text)} characters")
                return text
            except UnicodeDecodeError:
                logger.warning("UTF-8 decoding failed, trying latin-1...")
                try:
                    text = file_content.decode('latin-1')
                    logger.info(f"Text file content extracted with latin-1: {len(text)} characters")
                    return text
                except UnicodeDecodeError:
                    logger.error(f"Failed to decode text file {file.name}")
                    return f"Text file content from {file.name} (encoding issue)"

        else:
            logger.error(f"Unsupported file format: {file_extension}")
            return f"Unsupported file format: {file_extension}"

    except Exception as e:
        logger.error(f"Error extracting text from {file.name}: {str(e)}")
        return f"Error extracting content from {file.name}"













def generate_mock_scores(criteria_ids):
    """Generate mock scores for testing"""
    import random
    scores = {}
    for criteria_id in criteria_ids:
        scores[criteria_id] = random.randint(2, 5)  # Random score between 2-5
    return scores


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_evaluation_session(request):
    """Create a new bulk evaluation session"""
    try:
        data = json.loads(request.body)
        job_id = data.get('job_id')
        selected_criteria = data.get('criteria', [])

        if not job_id:
            return JsonResponse({'success': False, 'error': 'Job ID is required'})

        if len(selected_criteria) < 3:
            return JsonResponse({'success': False, 'error': 'At least 3 criteria must be selected'})

        # Get job
        try:
            job = Job.objects.get(id=job_id)
        except Job.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Job not found'})

        # Create evaluation session
        session = BulkEvaluationSession(
            job=job,
            user_id=str(request.user.id),
            session_name=f"Evaluation for {job.job_title}",
            selected_criteria=selected_criteria,
            status='pending'
        )
        session.save()

        logger.info(f"Created evaluation session {session.id} for job {job_id}")

        return JsonResponse({
            'success': True,
            'session_id': str(session.id),
            'session': session.to_dict()
        })

    except Exception as e:
        logger.error(f"Error creating evaluation session: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def upload_resume_to_session(request, session_id):
    """Upload resume files to an evaluation session"""
    try:
        if not session_id:
            return JsonResponse({'success': False, 'error': 'Session ID is required'})

        try:
            session = BulkEvaluationSession.objects.get(id=session_id)
        except BulkEvaluationSession.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Session not found'})

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        uploaded_resumes = []
        upload_order = UploadedResume.objects.filter(session=session, visible=True).count()

        # Process uploaded files
        for key, file in request.FILES.items():
            if key.startswith('resume_'):
                # Validate file type
                allowed_extensions = ['.pdf', '.doc', '.docx', '.txt']
                file_extension = '.' + file.name.split('.')[-1].lower()

                if file_extension not in allowed_extensions:
                    continue

                # Create uploaded resume record
                uploaded_resume = UploadedResume(
                    session=session,
                    filename=f"{session_id}_{upload_order}_{file.name}",
                    original_filename=file.name,
                    file_size=file.size,
                    upload_order=upload_order,
                    status='uploaded'
                )

                # Save file content
                uploaded_resume.file_content.put(file, content_type=file.content_type)
                uploaded_resume.save()

                uploaded_resumes.append(uploaded_resume.to_dict())
                upload_order += 1

        # Update session total resumes count
        session.total_resumes = UploadedResume.objects.filter(session=session, visible=True).count()
        session.updated_at = datetime.datetime.now()
        session.save()

        logger.info(f"Uploaded {len(uploaded_resumes)} resumes to session {session_id}")

        return JsonResponse({
            'success': True,
            'uploaded_resumes': uploaded_resumes,
            'total_resumes': session.total_resumes
        })

    except Exception as e:
        logger.error(f"Error uploading resumes: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@api_view(['GET'])
def get_evaluation_session(request, session_id):
    """Get evaluation session details"""
    try:
        session = BulkEvaluationSession.objects.get(id=session_id)

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Get uploaded resumes
        resumes = UploadedResume.objects.filter(session=session, visible=True).order_by('upload_order')

        # Get evaluation results if completed
        results = []
        if session.status == 'completed':
            evaluation_results = EvaluationResult.objects.filter(session=session).order_by('rank')
            results = [result.to_dict() for result in evaluation_results]

        return JsonResponse({
            'success': True,
            'session': session.to_dict(),
            'resumes': [resume.to_dict() for resume in resumes],
            'results': results,
            'task_status': task_manager.get_task_status(session_id)
        })

    except BulkEvaluationSession.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Session not found'})
    except Exception as e:
        logger.error(f"Error getting evaluation session: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def start_evaluation_task(request, session_id):
    """Start asynchronous evaluation for a session"""
    try:
        # session_id comes from URL parameter, not request body

        if not session_id:
            return JsonResponse({'success': False, 'error': 'Session ID is required'})

        try:
            session = BulkEvaluationSession.objects.get(id=session_id)
        except BulkEvaluationSession.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Session not found'})

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Check if there are resumes to evaluate
        resume_count = UploadedResume.objects.filter(session=session).count()
        if resume_count == 0:
            return JsonResponse({'success': False, 'error': 'No resumes uploaded for evaluation'})

        # Start evaluation task
        success = task_manager.start_evaluation(session_id)

        if success:
            return JsonResponse({
                'success': True,
                'message': 'Evaluation started successfully',
                'session_id': session_id
            })
        else:
            return JsonResponse({
                'success': False,
                'error': 'Failed to start evaluation task'
            })

    except Exception as e:
        logger.error(f"Error starting evaluation task: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_evaluation_status(request, session_id):
    """Get current status of evaluation task"""
    try:
        session = BulkEvaluationSession.objects.get(id=session_id)

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        task_status = task_manager.get_task_status(session_id)

        return JsonResponse({
            'success': True,
            'session_status': session.status,
            'task_status': task_status,
            'session': session.to_dict()
        })

    except BulkEvaluationSession.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Session not found'})
    except Exception as e:
        logger.error(f"Error getting evaluation status: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})


@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def remove_resume_from_session(request, session_id, resume_id):
    """Soft delete a resume from an evaluation session"""
    try:
        # Get session
        try:
            session = BulkEvaluationSession.objects.get(id=session_id)
        except BulkEvaluationSession.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Session not found'})

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Get resume
        try:
            resume = UploadedResume.objects.get(id=resume_id, session=session)
        except UploadedResume.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Resume not found'})

        # Soft delete by setting visible=False
        resume.visible = False
        resume.save()

        # Update session total resumes count
        remaining_count = UploadedResume.objects.filter(session=session, visible=True).count()
        session.total_resumes = remaining_count
        session.updated_at = datetime.datetime.now()
        session.save()

        # If no resumes left, delete the session
        if remaining_count == 0:
            print(f"No visible resumes left, deleting session {session_id}")
            logger.info(f"No visible resumes left, deleting session {session_id}")

            # Delete all related data
            UploadedResume.objects.filter(session=session).delete()  # Hard delete all resumes
            EvaluationResult.objects.filter(session=session).delete()  # Delete any results
            session.delete()  # Delete the session

            return JsonResponse({
                'success': True,
                'message': 'Resume and session deleted successfully',
                'session_deleted': True,
                'total_resumes': 0
            })

        logger.info(f"Soft deleted resume {resume_id} from session {session_id}")

        return JsonResponse({
            'success': True,
            'message': 'Resume removed successfully',
            'total_resumes': remaining_count,
            'session_deleted': False
        })

    except Exception as e:
        logger.error(f"Error removing resume from session: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})

@csrf_exempt
@require_http_methods(["DELETE"])
def clear_evaluation_results(request, session_id):
    """Clear all evaluation results for a session"""
    try:
        # Get the session
        session = BulkEvaluationSession.objects.get(id=session_id)

        # Delete all evaluation results for this session
        EvaluationResult.objects.filter(session=session).delete()

        # Reset session status to pending
        session.status = 'pending'
        session.save()

        logger.info(f"Cleared evaluation results for session {session_id}")

        return JsonResponse({
            'success': True,
            'message': 'Results cleared successfully'
        })

    except BulkEvaluationSession.DoesNotExist:
        return JsonResponse({
            'success': False,
            'error': 'Session not found'
        }, status=404)
    except Exception as e:
        logger.error(f"Error clearing evaluation results: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to clear results'
        }, status=500)


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_session_criteria(request, session_id):
    """Update the selected criteria for an evaluation session"""
    try:
        # Get session
        session = BulkEvaluationSession.objects.get(id=session_id)

        # Check if session belongs to user
        if session.user_id != str(request.user.id):
            return JsonResponse({'success': False, 'error': 'Permission denied'})

        # Parse request data
        data = json.loads(request.body)
        criteria = data.get('criteria', [])

        if len(criteria) < 3:
            return JsonResponse({'success': False, 'error': 'At least 3 criteria must be selected'})

        # Update session criteria
        session.selected_criteria = criteria
        session.updated_at = datetime.datetime.now()
        session.save()

        print(f"Updated session {session_id} criteria to: {criteria}")
        logger.info(f"Updated session {session_id} criteria to: {criteria}")

        return JsonResponse({
            'success': True,
            'message': 'Criteria updated successfully',
            'selected_criteria': criteria
        })

    except BulkEvaluationSession.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Session not found'})
    except Exception as e:
        logger.error(f"Error updating session criteria: {str(e)}")
        return JsonResponse({'success': False, 'error': str(e)})