from django.urls import path
from . import views

app_name = 'job_management'

urlpatterns = [
    # Job role management
    path('job-roles/', views.list_job_roles, name='list_job_roles'),
    path('job-roles/create/', views.create_job_role, name='create_job_role'),
    path('job-roles/<str:role_id>/', views.get_job_role_details, name='get_job_role_details'),
    path('job-roles/<str:role_id>/update/', views.update_job_role, name='update_job_role'),
    path('job-roles/<str:role_id>/delete/', views.delete_job_role, name='delete_job_role'),
    
    # Job management
    path('jobs/', views.list_jobs, name='list_jobs'),
    path('jobs/create/', views.create_job, name='create_job'),
    path('jobs/sync-from-zoho/', views.sync_jobs_from_zoho, name='sync_jobs_from_zoho'),
    path('jobs/over-time/', views.get_jobs_over_time, name='get_jobs_over_time'),
    path('jobs/by-title/', views.get_jobs_by_title, name='get_jobs_by_title'),
    path('jobs/test-zoho-credentials/', views.test_zoho_credentials, name='test_zoho_credentials'),
    path('jobs/<str:job_id>/', views.get_job_details, name='get_job_details'),
    path('jobs/<str:job_id>/update/', views.update_job, name='update_job'),
    path('jobs/<str:job_id>/delete/', views.delete_job, name='delete_job'),
    path('jobs/<str:job_id>/description/', views.update_job_description, name='update_job_description'),
    path('evaluate-resumes/', views.evaluate_resumes, name='evaluate_resumes'),

    # Bulk evaluation endpoints
    path('evaluation-sessions/create/', views.create_evaluation_session, name='create_evaluation_session'),
    path('evaluation-sessions/<str:session_id>/', views.get_evaluation_session, name='get_evaluation_session'),
    path('evaluation-sessions/<str:session_id>/upload/', views.upload_resume_to_session, name='upload_resume_to_session'),
    path('evaluation-sessions/<str:session_id>/resumes/<str:resume_id>/remove/', views.remove_resume_from_session, name='remove_resume_from_session'),
    path('evaluation-sessions/<str:session_id>/start/', views.start_evaluation_task, name='start_evaluation_task'),
    path('evaluation-sessions/<str:session_id>/status/', views.get_evaluation_status, name='get_evaluation_status'),
    path('evaluation-sessions/<str:session_id>/clear-results/', views.clear_evaluation_results, name='clear_evaluation_results'),
    path('evaluation-sessions/<str:session_id>/update-criteria/', views.update_session_criteria, name='update_session_criteria'),
]
