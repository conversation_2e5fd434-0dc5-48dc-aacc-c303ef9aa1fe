"""
LLM utility functions for resume evaluation
"""
import logging
from ai_settings.models import AIProviderConfig
from ai_settings.queue import make_llm_request
import datetime

logger = logging.getLogger(__name__)


def evaluate_resume_with_llm(resume_text, job_description, criteria_ids, criteria_map):
    """
    Evaluate a resume using LLM
    """
    try:
        logger.info("=== STARTING LLM EVALUATION ===")
        
        # Get active AI provider configuration
        try:
            from ai_settings.models import AISettings
            ai_settings = AISettings.get_current_settings()
            active_provider_config = ai_settings.get_active_provider_config()
        except Exception as e:
            logger.error(f"Error fetching AI provider config: {str(e)}")
            raise Exception("Failed to fetch AI provider configuration")

        if not active_provider_config:
            logger.error("No active provider configuration found")
            raise Exception("No AI provider configured. Please configure an AI provider in Settings.")
            
        # Auto-configure Ollama if it's not configured but is working
        if not active_provider_config.is_configured and active_provider_config.provider_name.lower() == 'ollama':
            logger.info("Attempting to auto-configure Ollama...")
            try:
                import requests
                response = requests.get(f"{active_provider_config.llm_url}/api/tags", timeout=10)
                if response.status_code == 200:
                    active_provider_config.is_configured = True
                    
                    # Also fetch and save available models
                    models_data = response.json()
                    models = [model['name'] for model in models_data.get('models', [])]
                    active_provider_config.available_models = models
                    
                    # Set a default model if none is selected
                    if not active_provider_config.selected_model and models:
                        active_provider_config.selected_model = models[0]
                    
                    active_provider_config.save()
                    logger.info(f"Auto-configured Ollama successfully. Available models: {models}")
                else:
                    logger.error(f"Ollama connection test failed: HTTP {response.status_code}")
                    raise Exception(f"Ollama is not running or not accessible at {active_provider_config.llm_url}")
            except requests.exceptions.RequestException as e:
                logger.error(f"Failed to connect to Ollama: {str(e)}")
                raise Exception(f"Cannot connect to Ollama at {active_provider_config.llm_url}. Please ensure Ollama is running.")
            except Exception as e:
                logger.error(f"Error auto-configuring Ollama: {str(e)}")
                raise Exception(f"Failed to auto-configure Ollama: {str(e)}")
        
        if not active_provider_config.is_configured:
            logger.error(f"Provider {active_provider_config.provider_name} is not configured")
            raise Exception(f"AI provider {active_provider_config.provider_name} is not configured. Please configure it in Settings.")

        logger.info(f"Using AI provider: {active_provider_config.provider_name}")
        logger.info(f"Provider configured: {active_provider_config.is_configured}")
        logger.info(f"Selected model: {active_provider_config.selected_model}")
        
        # Validate model selection
        if not active_provider_config.selected_model:
            logger.error("No model selected for LLM request")
            # Try to auto-select a model if available
            if active_provider_config.available_models:
                active_provider_config.selected_model = active_provider_config.available_models[0]
                active_provider_config.save()
                logger.info(f"Auto-selected model: {active_provider_config.selected_model}")
            else:
                raise Exception("No model selected and no models available. Please configure AI provider in Settings.")

        # Create evaluation prompt
        criteria_descriptions = []
        for criteria_id in criteria_ids:
            criteria_name = criteria_map.get(criteria_id, criteria_id)
            criteria_descriptions.append(f"- {criteria_name} ({criteria_id})")

        prompt = f"""
You are an expert HR professional evaluating a resume for a specific job position. 

JOB DESCRIPTION:
{job_description}

RESUME TO EVALUATE:
{resume_text}

EVALUATION CRITERIA:
{chr(10).join(criteria_descriptions)}

SCORING GUIDELINES (BE STRICT AND REALISTIC):
- 5: Exceptional match - Candidate significantly exceeds ALL requirements with proven expertise and leadership
- 4: Strong match - Candidate clearly meets ALL requirements with solid additional strengths and experience
- 3: Good match - Candidate meets MOST requirements adequately with minor gaps
- 2: Partial match - Candidate meets SOME requirements but has notable gaps or lacks experience
- 1: Weak match - Candidate barely meets minimum requirements with significant deficiencies
- 0: No match - Candidate does not meet this requirement at all

IMPORTANT: Be conservative in your scoring. Most candidates should score between 1-3. Only give 4-5 for truly exceptional candidates who clearly demonstrate superior qualifications. Consider years of experience, depth of skills, and specific achievements mentioned.

Please analyze the resume thoroughly and provide realistic scores based on:
1. Relevant work experience and duration (exact years matter - be strict about experience requirements)
2. Technical skills and certifications mentioned (look for specific technologies, not just buzzwords)
3. Educational background alignment (consider degree relevance and institution quality)
4. Industry experience relevance (same industry experience should be weighted heavily)
5. Leadership and project management experience (look for concrete examples, team sizes, project outcomes)
6. Communication and soft skills indicators (evidence of presentations, documentation, mentoring)

EVALUATION APPROACH:
- Compare the candidate's background directly against the job requirements
- Look for specific achievements, not just job titles
- Consider the depth of experience, not just breadth
- Be skeptical of generic claims without supporting details
- Penalize significant gaps in required skills or experience
- Reward concrete examples of success and measurable achievements

Provide your evaluation in the following JSON format EXACTLY as shown:
{{
    "scores": {{
        {', '.join([f'"{cid}": 3' for cid in criteria_ids])}
    }},
    "comments": {{
        {', '.join([f'"{cid}": "Brief explanation for the score"' for cid in criteria_ids])}
    }}
}}

CRITICAL FORMATTING RULES:
1. Each score MUST be a single integer from 0-5 (NOT an array, NOT a string)
2. Use exactly this format: "criteria_id": 3 (where 3 is your actual score)
3. Do NOT use arrays like [3] or strings like "3"
4. Do NOT add extra brackets or formatting
5. Return ONLY the JSON object - no markdown, no explanations, no code blocks

EXAMPLE of CORRECT format:
{{
    "scores": {{
        "skills_match": 3,
        "experience_relevance": 2
    }},
    "comments": {{
        "skills_match": "Good technical skills but lacks cloud experience",
        "experience_relevance": "Limited years of experience for senior role"
    }}
}}

For each criterion, provide:
1. A score from 0-5 (as a plain integer)
2. A brief comment (1-2 sentences) explaining why you gave that score
"""

        print(f"=== STARTING LLM REQUEST TO {active_provider_config.provider_name} ===")
        logger.info("Starting LLM request to " + active_provider_config.provider_name)

        # Make LLM request
        print(f"Sending prompt to LLM ({len(prompt)} characters)")
        logger.info(f"Sending prompt to LLM ({len(prompt)} characters)")

        response = make_llm_request(prompt, active_provider_config)

        print(f"LLM Response received: {response}")
        logger.info(f"LLM Response received: {response}")
        
        if not response.get('success'):
            error_msg = response.get('error', 'Unknown error')
            print(f"ERROR: LLM request failed: {error_msg}")
            logger.error(f"LLM request failed: {error_msg}")
            raise Exception(f"AI request failed: {error_msg}")

        if 'response' not in response:
            print(f"ERROR: LLM request returned no response: {response}")
            logger.error("LLM request failed or returned no response")
            logger.error(f"Response details: {response}")
            raise Exception("AI request failed or returned no response")

        print("✅ LLM request completed successfully")
        logger.info("LLM request completed successfully")

        # Parse JSON response
        import json
        import re
        try:
            print("=== PARSING LLM RESPONSE ===")
            logger.info("Parsing JSON response...")
            raw_response = response['response']
            print(f"Raw LLM response: {raw_response}")
            logger.info(f"Raw response to parse: {raw_response}")
            
            # Clean the response - remove markdown code blocks if present
            cleaned_response = raw_response.strip()
            
            # Remove markdown code blocks (```json ... ``` or ``` ... ```)
            if cleaned_response.startswith('```'):
                logger.info("Detected markdown code blocks, cleaning...")
                # Find the JSON content between code blocks
                json_match = re.search(r'```(?:json)?\s*\n?(.*?)\n?```', cleaned_response, re.DOTALL)
                if json_match:
                    cleaned_response = json_match.group(1).strip()
                    logger.info(f"Extracted JSON from markdown: {cleaned_response}")
                else:
                    # Fallback: remove all ``` markers
                    cleaned_response = re.sub(r'```[a-zA-Z]*\n?', '', cleaned_response)
                    cleaned_response = cleaned_response.replace('```', '').strip()
                    logger.info(f"Fallback cleaning result: {cleaned_response}")
            
            # Try to parse the cleaned JSON
            result = json.loads(cleaned_response)
            logger.info(f"JSON parsed successfully. Keys: {list(result.keys())}")
            
            if 'scores' in result:
                logger.info("Scores found in response")
                # Validate and clean scores
                scores = {}
                comments = {}
                
                for criteria_id in criteria_ids:
                    raw_score = result['scores'].get(criteria_id, 0)
                    print(f"DEBUG: Raw score for {criteria_id}: {raw_score} (type: {type(raw_score)})")

                    # FLEXIBLE PARSING: Handle different score formats from different models
                    try:
                        if isinstance(raw_score, list):
                            # Handle array format: [3] -> 3
                            clean_score = int(raw_score[0]) if raw_score else 0
                            print(f"Converted array {raw_score} to {clean_score}")
                        elif isinstance(raw_score, str):
                            # Handle string format: "3" -> 3
                            clean_score = int(raw_score)
                            print(f"Converted string '{raw_score}' to {clean_score}")
                        elif isinstance(raw_score, (int, float)):
                            # Handle numeric format: 3 -> 3
                            clean_score = int(raw_score)
                        else:
                            print(f"WARNING: Unknown score format for {criteria_id}: {raw_score}")
                            clean_score = 0

                        # Ensure score is between 0-5
                        clean_score = max(0, min(5, clean_score))
                        scores[criteria_id] = clean_score
                        logger.info(f"Criteria {criteria_id}: {raw_score} -> {clean_score}")

                    except (ValueError, TypeError, IndexError) as e:
                        print(f"ERROR parsing score for {criteria_id}: {raw_score}, error: {e}")
                        logger.error(f"Error parsing score for {criteria_id}: {raw_score}, error: {e}")
                        scores[criteria_id] = 0
                    
                    # Get comment if available
                    if 'comments' in result:
                        comment = result['comments'].get(criteria_id, '')
                        comments[criteria_id] = comment
                        logger.info(f"Comment for {criteria_id}: {comment[:100]}...")
                
                logger.info(f"Final parsed scores: {scores}")
                logger.info(f"Comments available: {len(comments)} out of {len(criteria_ids)}")
                logger.info("=== LLM EVALUATION COMPLETED SUCCESSFULLY ===")
                
                # Return both scores and comments
                return {
                    'scores': scores,
                    'comments': comments
                }
            else:
                logger.error("No 'scores' key found in AI response")
                raise Exception("AI response does not contain scores")
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.error(f"Failed to parse LLM response: {str(e)}")
            logger.error(f"Raw response: {response['response']}")
            logger.error(f"Cleaned response: {cleaned_response}")
            raise Exception(f"AI response parsing failed: {str(e)}")

    except Exception as e:
        logger.error(f"=== LLM EVALUATION FAILED ===")
        logger.error(f"Critical error in LLM evaluation: {str(e)}")
        logger.error("=== LLM EVALUATION FAILED ===")
        raise Exception(f"AI evaluation failed: {str(e)}")
