import logging
from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from admin_controls.utils import check_admin_permission

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def list_interviews(request):
    """List all interviews with pagination and filtering"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        from .models import Interview

        # Get query parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 20))
        search = request.GET.get('search', '')

        # Build query
        queryset = Interview.objects()

        # Apply search filter
        if search:
            from mongoengine import Q
            queryset = queryset.filter(
                Q(title__icontains=search) | Q(candidate_id__icontains=search)
            )

        # Get total count
        total_count = queryset.count()

        # Apply pagination
        skip = (page - 1) * per_page
        interviews = queryset.skip(skip).limit(per_page).order_by('-created_at')

        # Convert to list of dictionaries
        interview_data = []
        for interview in interviews:
            interview_dict = interview.to_dict()

            # Get candidate details
            try:
                from candidate_management.models import Candidate
                candidate = Candidate.objects.get(id=interview.candidate_id)
                interview_dict['candidate_name'] = candidate.full_name
                interview_dict['candidate_email'] = candidate.email
            except:
                interview_dict['candidate_name'] = 'Unknown Candidate'
                interview_dict['candidate_email'] = ''

            interview_data.append(interview_dict)

        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_previous = page > 1

        return Response({
            'success': True,
            'data': interview_data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_count': total_count,
                'has_next': has_next,
                'has_previous': has_previous,
                'per_page': per_page
            }
        })

    except Exception as e:
        logger.error(f"Error in list_interviews: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to fetch interviews'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_interview(request):
    """Create a new interview"""
    try:
        if not check_admin_permission(request.user):
            return Response({
                'success': False,
                'error': 'Permission denied'
            }, status=status.HTTP_403_FORBIDDEN)

        from .models import Interview

        data = request.data

        # Required fields
        title = data.get('title', '').strip()
        candidate_id = data.get('candidate_id', '').strip()

        if not title:
            return Response({
                'success': False,
                'error': 'Interview title is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not candidate_id:
            return Response({
                'success': False,
                'error': 'Candidate is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create interview
        interview = Interview(
            title=title,
            description=data.get('description', ''),
            scheduled_date=data.get('scheduled_date', ''),
            scheduled_time=data.get('scheduled_time', ''),
            duration_minutes=data.get('duration_minutes', '60'),
            candidate_id=candidate_id,
            interviewer_ids=data.get('panel_members', '').split(',') if data.get('panel_members') else [],
            status=data.get('status', 'Scheduled'),
            interview_type=data.get('interview_type', 'Technical'),
            interview_mode=data.get('interview_mode', 'Video Call'),
            meeting_link=data.get('meeting_link', ''),
            meeting_room=data.get('meeting_room', ''),
            feedback=data.get('feedback', ''),
            scoring_criteria=data.get('scoring_criteria', ''),
            score=data.get('score', ''),
            result=data.get('result', 'Pending'),
            created_by=str(request.user.id)
        )

        interview.save()

        logger.info(f"Interview scheduled: {interview.title} by {request.user.email}")

        return Response({
            'success': True,
            'message': 'Interview scheduled successfully',
            'data': interview.to_dict()
        })

    except Exception as e:
        logger.error(f"Error in create_interview: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to schedule interview'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
