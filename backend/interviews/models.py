from mongoengine import Document, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ield, <PERSON><PERSON>anField, EmbeddedDocument, EmbeddedDocument<PERSON>ield, ListField
import datetime

class Interview(Document):
    """
    Interview model stored in MongoDB
    """
    # Basic Information
    title = StringField(required=True, max_length=200)
    description = StringField(max_length=1000)

    # Scheduling
    scheduled_date = StringField(max_length=20)  # Date as string
    scheduled_time = StringField(max_length=10)  # Time as string
    duration_minutes = StringField(max_length=10)

    # Participants
    candidate_id = StringField(required=True)  # Reference to Candidate
    interviewer_ids = ListField(StringField())  # List of interviewer user IDs

    # Status and Type
    status = StringField(choices=['Scheduled', 'In Progress', 'Completed', 'Cancelled', 'Rescheduled'], default='Scheduled')
    interview_type = StringField(choices=['Technical', 'HR', 'Managerial', 'Final'], default='Technical')
    interview_mode = StringField(choices=['In-Person', 'Video Call', 'Phone Call'], default='Video Call')

    # Meeting Details
    meeting_link = StringField(max_length=500)
    meeting_room = StringField(max_length=100)

    # Results
    feedback = StringField(max_length=2000)
    scoring_criteria = StringField(max_length=3000)
    score = StringField(max_length=10)
    result = StringField(choices=['Selected', 'Rejected', 'On Hold', 'Pending'], default='Pending')

    # Metadata
    created_at = DateTimeField(default=datetime.datetime.now)
    updated_at = DateTimeField(default=datetime.datetime.now)
    created_by = StringField()  # User ID who created the interview

    meta = {
        'collection': 'interviews',
        'indexes': [
            'candidate_id',
            'scheduled_date',
            'status',
            'interview_type',
            'created_at',
        ]
    }

    def __str__(self):
        return f"{self.title} - {self.scheduled_date}"

    def save(self, *args, **kwargs):
        self.updated_at = datetime.datetime.now()
        return super(Interview, self).save(*args, **kwargs)

    def to_dict(self):
        """Convert interview to dictionary for API responses"""
        return {
            'id': str(self.id),
            'title': self.title,
            'description': self.description,
            'scheduled_date': self.scheduled_date,
            'scheduled_time': self.scheduled_time,
            'duration_minutes': self.duration_minutes,
            'candidate_id': self.candidate_id,
            'interviewer_ids': self.interviewer_ids,
            'status': self.status,
            'interview_type': self.interview_type,
            'interview_mode': self.interview_mode,
            'meeting_link': self.meeting_link,
            'meeting_room': self.meeting_room,
            'feedback': self.feedback,
            'scoring_criteria': self.scoring_criteria,
            'score': self.score,
            'result': self.result,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'created_by': self.created_by,
        }
