"""
URL configuration for talent_hero project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/auth/', include('accounts.urls')),
    path('api/core/', include('core.urls')),
    path('api/admin-controls/', include('admin_controls.urls')),
    path('api/vendor-management/', include('vendor_management.urls')),
    path('api/job-management/', include('job_management.urls')),
    path('api/candidate-management/', include('candidate_management.urls')),
    path('api/interviews/', include('interviews.urls')),
    path('api/settings/', include('ai_settings.urls')),
]
