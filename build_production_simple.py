#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Production Build Script for Talent Hero v3.11
Builds the application for production deployment without subprocess complexity
"""

import os
import shutil
import sys
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def switch_to_production():
    """Switch to production environment by copying files"""
    print("[INFO] Switching to production environment...")
    
    # Backend environment
    backend_dir = Path("backend")
    backend_prod_env = backend_dir / ".env.production"
    backend_env = backend_dir / ".env"
    
    if backend_prod_env.exists():
        shutil.copy2(backend_prod_env, backend_env)
        print("[OK] Backend: Switched to production configuration")
    else:
        print("[ERROR] Backend: Production .env.production not found")
        return False
    
    # Frontend environment
    frontend_dir = Path("frontend")
    frontend_prod_env = frontend_dir / ".env.production"
    frontend_env = frontend_dir / ".env"
    
    if frontend_prod_env.exists():
        shutil.copy2(frontend_prod_env, frontend_env)
        print("[OK] Frontend: Switched to production configuration")
    else:
        print("[ERROR] Frontend: Production .env.production not found")
        return False
    
    return True

def main():
    print("[INFO] Building Talent Hero v3.11 for production...")
    
    # Step 1: Switch to production environment
    print("\n[STEP 1] Switching to production environment...")
    if not switch_to_production():
        print("[ERROR] Failed to switch to production environment")
        sys.exit(1)
    
    # Step 2: Build frontend
    print("\n[STEP 2] Building frontend...")
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("[ERROR] Frontend directory not found")
        sys.exit(1)
    
    # Change to frontend directory and run build
    original_dir = os.getcwd()
    try:
        os.chdir(frontend_dir)
        
        print("[INFO] Installing frontend dependencies...")
        result = os.system("npm install")
        if result != 0:
            print("[ERROR] Failed to install frontend dependencies")
            sys.exit(1)
        print("[OK] Frontend dependencies installed")
        
        print("[INFO] Building frontend for production...")
        result = os.system("npm run build")
        if result != 0:
            print("[ERROR] Failed to build frontend")
            sys.exit(1)
        print("[OK] Frontend build completed")
        
    finally:
        os.chdir(original_dir)
    
    # Step 3: Prepare backend
    print("\n[STEP 3] Preparing backend...")
    backend_dir = Path("backend")
    
    if not backend_dir.exists():
        print("[ERROR] Backend directory not found")
        sys.exit(1)
    
    # Check if virtual environment exists
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("[INFO] Creating virtual environment...")
        os.chdir(backend_dir)
        result = os.system("python -m venv venv")
        if result != 0:
            print("[ERROR] Failed to create virtual environment")
            sys.exit(1)
        os.chdir(original_dir)
        print("[OK] Virtual environment created")
    
    # Change to backend directory and run setup
    try:
        os.chdir(backend_dir)
        
        # Install dependencies
        print("[INFO] Installing backend dependencies...")
        if sys.platform.startswith('win'):
            result = os.system("venv\\Scripts\\activate && pip install -r requirements.txt")
        else:
            result = os.system("source venv/bin/activate && pip install -r requirements.txt")
        
        if result != 0:
            print("[ERROR] Failed to install backend dependencies")
            sys.exit(1)
        print("[OK] Backend dependencies installed")
        
        # Collect static files
        print("[INFO] Collecting static files...")
        if sys.platform.startswith('win'):
            result = os.system("venv\\Scripts\\activate && python manage.py collectstatic --noinput")
        else:
            result = os.system("source venv/bin/activate && python manage.py collectstatic --noinput")
        
        if result != 0:
            print("[WARN] Static files collection failed (this might be normal)")
        else:
            print("[OK] Static files collected")
        
        # Run migrations
        print("[INFO] Running database migrations...")
        if sys.platform.startswith('win'):
            result = os.system("venv\\Scripts\\activate && python manage.py migrate")
        else:
            result = os.system("source venv/bin/activate && python manage.py migrate")
        
        if result != 0:
            print("[WARN] Database migrations failed (database might not be running)")
        else:
            print("[OK] Database migrations completed")
        
    finally:
        os.chdir(original_dir)
    
    print("\n[SUCCESS] Production build completed successfully!")
    print("\n[INFO] Next steps:")
    print("   1. Configure your nginx server with the provided config")
    print("   2. Start the backend: cd backend && venv\\Scripts\\activate && python manage.py runserver 0.0.0.0:8000")
    print("   3. The frontend build is in frontend/dist/")
    print("   4. Access your application at: https://talenthero.bceglobaltech.com")

if __name__ == "__main__":
    main()
