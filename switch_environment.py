#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Environment Switcher for Talent Hero v3.11
Switches between development and production configurations
"""

import os
import shutil
import sys
import argparse
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def switch_backend_env(env_type):
    """Switch backend environment configuration"""
    backend_dir = Path("backend")
    
    if env_type == "development":
        # Copy development .env if it exists, otherwise keep current
        dev_env = backend_dir / ".env"
        if dev_env.exists():
            print("[OK] Backend: Using development configuration")
        else:
            print("[WARN] Backend: Development .env not found, creating default")
            # Create default development .env
            with open(dev_env, 'w') as f:
                f.write("""# Database Configuration
POSTGRES_DB=th
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres123
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

MONGODB_DB=th
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/

# Django Configuration
SECRET_KEY=django-insecure-your-secret-key-change-this-in-production
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,talenthero.bceglobaltech.com

# Application Configuration
APP_HOST=localhost
APP_PORT=8000
FRONTEND_URL=http://localhost:5173

# Production Domain Configuration
PRODUCTION_DOMAIN=talenthero.bceglobaltech.com
PRODUCTION_FRONTEND_URL=https://talenthero.bceglobaltech.com
PRODUCTION_BACKEND_URL=https://talenthero.bceglobaltech.com/api

# Security
CORS_ALLOWED_ORIGINS=http://localhost:5173,http://localhost:5174,https://talenthero.bceglobaltech.com
CSRF_TRUSTED_ORIGINS=http://localhost:5173,http://localhost:5174,https://talenthero.bceglobaltech.com

# Session Configuration
SESSION_COOKIE_AGE=86400
SESSION_COOKIE_SECURE=False
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Lax

client_id=1000.80P8NXCX0O1UE7SWVRXK2TW65ZI82C
client_secret=e66233b30492f2696347e6b3fd2962eaf189715b38
""")
    
    elif env_type == "production":
        # Copy production .env
        prod_env = backend_dir / ".env.production"
        target_env = backend_dir / ".env"
        
        if prod_env.exists():
            shutil.copy2(prod_env, target_env)
            print("[OK] Backend: Switched to production configuration")
        else:
            print("[ERROR] Backend: Production .env.production not found")
            return False
    
    return True

def switch_frontend_env(env_type):
    """Switch frontend environment configuration"""
    frontend_dir = Path("frontend")
    
    if env_type == "development":
        # Copy development .env
        dev_env = frontend_dir / ".env"
        if not dev_env.exists():
            # Create default development .env
            with open(dev_env, 'w') as f:
                f.write("""# Development Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_ENV=development

# Production Configuration (uncomment for production)
# VITE_API_BASE_URL=https://talenthero.bceglobaltech.com/api
# VITE_APP_ENV=production
""")
        print("[OK] Frontend: Using development configuration")
    
    elif env_type == "production":
        # Copy production .env
        prod_env = frontend_dir / ".env.production"
        target_env = frontend_dir / ".env"
        
        if prod_env.exists():
            shutil.copy2(prod_env, target_env)
            print("[OK] Frontend: Switched to production configuration")
        else:
            print("[ERROR] Frontend: Production .env.production not found")
            return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='Switch between development and production environments')
    parser.add_argument('environment', choices=['development', 'production', 'dev', 'prod'], 
                       help='Environment to switch to')
    
    args = parser.parse_args()
    
    # Normalize environment names
    env_type = "development" if args.environment in ['development', 'dev'] else "production"
    
    print(f"[INFO] Switching to {env_type} environment...")

    # Switch backend environment
    backend_success = switch_backend_env(env_type)

    # Switch frontend environment
    frontend_success = switch_frontend_env(env_type)

    if backend_success and frontend_success:
        print(f"\n[SUCCESS] Successfully switched to {env_type} environment!")

        if env_type == "development":
            print("\n[INFO] Next steps for development:")
            print("   Backend: cd backend && venv\\Scripts\\activate && python manage.py runserver")
            print("   Frontend: cd frontend && npm run dev")
            print("   Access: http://localhost:5173")
        else:
            print("\n[INFO] Next steps for production:")
            print("   Backend: cd backend && venv\\Scripts\\activate && python manage.py runserver 0.0.0.0:8000")
            print("   Frontend: cd frontend && npm run build && npm run preview")
            print("   Access: https://talenthero.bceglobaltech.com")
            print("\n[WARN] Make sure your nginx configuration is properly set up!")
    else:
        print(f"\n[ERROR] Failed to switch to {env_type} environment")
        sys.exit(1)

if __name__ == "__main__":
    main()
