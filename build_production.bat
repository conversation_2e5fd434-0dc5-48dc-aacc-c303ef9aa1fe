@echo off
chcp 65001 >nul
echo [INFO] Building Talent Hero v3.11 for production...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "backend\venv" (
    echo [ERROR] Virtual environment not found at backend\venv
    echo [INFO] Creating virtual environment...
    cd backend
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] Failed to create virtual environment
        pause
        exit /b 1
    )
    cd ..
    echo [OK] Virtual environment created
)

REM Run the Python build script
python build_production.py
if errorlevel 1 (
    echo [ERROR] Build failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] Build completed successfully!
pause
