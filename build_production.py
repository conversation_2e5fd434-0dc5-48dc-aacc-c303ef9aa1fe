#!/usr/bin/env python3
"""
Production Build Script for Talent Hero v3.11
Builds the application for production deployment
"""

import os
import subprocess
import sys
from pathlib import Path

def run_command(command, cwd=None, description=""):
    """Run a command and handle errors"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def build_frontend():
    """Build frontend for production"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Install dependencies
    if not run_command("npm install", cwd=frontend_dir, description="Installing frontend dependencies"):
        return False
    
    # Build for production
    if not run_command("npm run build", cwd=frontend_dir, description="Building frontend for production"):
        return False
    
    return True

def prepare_backend():
    """Prepare backend for production"""
    backend_dir = Path("backend")
    
    if not backend_dir.exists():
        print("❌ Backend directory not found")
        return False
    
    # Install Python dependencies
    if not run_command("pip install -r requirements.txt", cwd=backend_dir, 
                      description="Installing backend dependencies"):
        return False
    
    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", cwd=backend_dir,
                      description="Collecting static files"):
        return False
    
    # Run migrations
    if not run_command("python manage.py migrate", cwd=backend_dir,
                      description="Running database migrations"):
        return False
    
    return True

def main():
    print("🚀 Building Talent Hero v3.11 for production...")
    
    # Switch to production environment
    print("\n1️⃣ Switching to production environment...")
    if not run_command("python switch_environment.py production", 
                      description="Switching to production environment"):
        sys.exit(1)
    
    # Build frontend
    print("\n2️⃣ Building frontend...")
    if not build_frontend():
        print("❌ Frontend build failed")
        sys.exit(1)
    
    # Prepare backend
    print("\n3️⃣ Preparing backend...")
    if not prepare_backend():
        print("❌ Backend preparation failed")
        sys.exit(1)
    
    print("\n🎉 Production build completed successfully!")
    print("\n📝 Next steps:")
    print("   1. Configure your nginx server with the provided config")
    print("   2. Start the backend: cd backend && python manage.py runserver 0.0.0.0:8000")
    print("   3. The frontend build is in frontend/dist/")
    print("   4. Access your application at: https://talenthero.bceglobaltech.com")

if __name__ == "__main__":
    main()
