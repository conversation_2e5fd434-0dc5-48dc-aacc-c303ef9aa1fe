#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Production Build Script for Talent Hero v3.11
Builds the application for production deployment
"""

import os
import subprocess
import sys
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def get_venv_command(base_command, backend_dir):
    """Get command with virtual environment activation"""
    venv_path = backend_dir / "venv"
    if sys.platform.startswith('win'):
        activate_cmd = f"{venv_path}\\Scripts\\activate"
        return f"{activate_cmd} && {base_command}"
    else:
        activate_cmd = f"source {venv_path}/bin/activate"
        return f"{activate_cmd} && {base_command}"

def run_command(command, cwd=None, description="", use_venv=False):
    """Run a command and handle errors"""
    print(f"[INFO] {description}")

    # Use virtual environment if requested and we're in backend
    if use_venv and cwd and "backend" in str(cwd):
        command = get_venv_command(command, cwd)

    try:
        result = subprocess.run(command, shell=True, cwd=cwd, check=True,
                              capture_output=True, text=True)
        print(f"[OK] {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def build_frontend():
    """Build frontend for production"""
    frontend_dir = Path("frontend")

    if not frontend_dir.exists():
        print("[ERROR] Frontend directory not found")
        return False

    # Install dependencies
    if not run_command("npm install", cwd=frontend_dir, description="Installing frontend dependencies"):
        return False

    # Build for production
    if not run_command("npm run build", cwd=frontend_dir, description="Building frontend for production"):
        return False

    return True

def prepare_backend():
    """Prepare backend for production"""
    backend_dir = Path("backend")

    if not backend_dir.exists():
        print("[ERROR] Backend directory not found")
        return False

    # Check if virtual environment exists, create if needed
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("[INFO] Virtual environment not found, creating it...")
        if not run_command("python -m venv venv", cwd=backend_dir,
                          description="Creating virtual environment"):
            return False

    # Install Python dependencies
    if not run_command("pip install -r requirements.txt", cwd=backend_dir,
                      description="Installing backend dependencies", use_venv=True):
        return False

    # Collect static files
    if not run_command("python manage.py collectstatic --noinput", cwd=backend_dir,
                      description="Collecting static files", use_venv=True):
        return False

    # Run migrations
    if not run_command("python manage.py migrate", cwd=backend_dir,
                      description="Running database migrations", use_venv=True):
        return False

    return True

def main():
    print("[INFO] Building Talent Hero v3.11 for production...")

    # Switch to production environment
    print("\n[STEP 1] Switching to production environment...")
    if not run_command("python switch_environment.py production",
                      description="Switching to production environment"):
        sys.exit(1)

    # Build frontend
    print("\n[STEP 2] Building frontend...")
    if not build_frontend():
        print("[ERROR] Frontend build failed")
        sys.exit(1)

    # Prepare backend
    print("\n[STEP 3] Preparing backend...")
    if not prepare_backend():
        print("[ERROR] Backend preparation failed")
        sys.exit(1)

    print("\n[SUCCESS] Production build completed successfully!")
    print("\n[INFO] Next steps:")
    print("   1. Configure your nginx server with the provided config")
    print("   2. Start the backend: cd backend && venv\\Scripts\\activate && python manage.py runserver 0.0.0.0:8000")
    print("   3. The frontend build is in frontend/dist/")
    print("   4. Access your application at: https://talenthero.bceglobaltech.com")

if __name__ == "__main__":
    main()
