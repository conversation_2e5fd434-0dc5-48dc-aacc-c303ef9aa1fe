import { useState, useEffect } from 'react'
import { X, Calendar, User, Briefcase, Search, Upload, Clock, Users, Video, FileText } from 'lucide-react'

interface Job {
  id: string
  ticket_id: string
  job_title: string
  job_role_name?: string
}

interface Candidate {
  id: string
  full_name: string
  candidate_id: string
  email: string
  preferred_role_name?: string
}

interface ScheduleInterviewModalProps {
  isOpen: boolean
  onClose: () => void
  onInterviewScheduled: () => void
}

export default function ScheduleInterviewModal({ isOpen, onClose, onInterviewScheduled }: ScheduleInterviewModalProps) {
  // Form state
  const [jobSearch, setJobSearch] = useState('')
  const [candidateSearch, setCandidateSearch] = useState('')
  const [selectedJob, setSelectedJob] = useState<Job | null>(null)
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null)
  const [interviewLevel, setInterviewLevel] = useState('')
  const [scheduledDate, setScheduledDate] = useState('')
  const [scheduledTime, setScheduledTime] = useState('')
  const [duration, setDuration] = useState('60')
  const [interviewMode, setInterviewMode] = useState('Video Call')
  const [meetingLink, setMeetingLink] = useState('')
  const [meetingRoom, setMeetingRoom] = useState('')
  const [panelMembers, setPanelMembers] = useState('')
  const [interviewType, setInterviewType] = useState('Technical')
  const [transcriptFile, setTranscriptFile] = useState<File | null>(null)
  const [completionStatus, setCompletionStatus] = useState('Scheduled')
  const [interviewResult, setInterviewResult] = useState('Pending')
  const [feedback, setFeedback] = useState('')
  const [scoringCriteria, setScoringCriteria] = useState('')
  const [score, setScore] = useState('')

  // Search results
  const [jobResults, setJobResults] = useState<Job[]>([])
  const [candidateResults, setCandidateResults] = useState<Candidate[]>([])
  const [showJobResults, setShowJobResults] = useState(false)
  const [showCandidateResults, setShowCandidateResults] = useState(false)

  // Other state
  const [loading, setLoading] = useState(false)
  const [scheduling, setScheduling] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen) {
      resetForm()
    }
  }, [isOpen])

  // Search for jobs
  useEffect(() => {
    if (jobSearch.length >= 2) {
      searchJobs()
    } else {
      setJobResults([])
      setShowJobResults(false)
    }
  }, [jobSearch])

  // Search for candidates
  useEffect(() => {
    if (candidateSearch.length >= 2) {
      searchCandidates()
    } else {
      setCandidateResults([])
      setShowCandidateResults(false)
    }
  }, [candidateSearch])

  const resetForm = () => {
    setJobSearch('')
    setCandidateSearch('')
    setSelectedJob(null)
    setSelectedCandidate(null)
    setInterviewLevel('')
    setScheduledDate('')
    setScheduledTime('')
    setDuration('60')
    setInterviewMode('Video Call')
    setMeetingLink('')
    setMeetingRoom('')
    setPanelMembers('')
    setInterviewType('Technical')
    setTranscriptFile(null)
    setCompletionStatus('Scheduled')
    setInterviewResult('Pending')
    setFeedback('')
    setScoringCriteria('')
    setScore('')
    setError('')
  }

  const searchJobs = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/job-management/jobs/?search=${encodeURIComponent(jobSearch)}&per_page=5`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Fix API response structure - jobs are in data.data.jobs
          const jobs = data.data?.jobs || []
          setJobResults(jobs.slice(0, 5)) // Limit to 5 results
          setShowJobResults(jobs.length > 0)
        }
      }
    } catch (error) {
      console.error('Error searching jobs:', error)
    }
  }

  const searchCandidates = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/candidate-management/candidates/?search=${encodeURIComponent(candidateSearch)}&per_page=5`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Candidates are directly in data.data
          const candidates = data.data || []
          setCandidateResults(candidates.slice(0, 5)) // Limit to 5 results
          setShowCandidateResults(candidates.length > 0)
        }
      }
    } catch (error) {
      console.error('Error searching candidates:', error)
    }
  }

  const handleJobSelect = (job: Job) => {
    setSelectedJob(job)
    setJobSearch(`${job.ticket_id} - ${job.job_title}`)
    setShowJobResults(false)
  }

  const handleCandidateSelect = (candidate: Candidate) => {
    setSelectedCandidate(candidate)
    setCandidateSearch(`${candidate.candidate_id} - ${candidate.full_name}`)
    setShowCandidateResults(false)
  }

  const handleTranscriptFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setTranscriptFile(file)
    }
  }

  const handleAIGenerateCriteria = async () => {
    if (!selectedJob || !interviewType || !interviewLevel) {
      setError('Please select a job, interview type, and level first')
      return
    }

    try {
      // Generate AI-based scoring criteria based on job and interview details
      const criteria = generateScoringCriteria(selectedJob, interviewType, interviewLevel)
      setScoringCriteria(criteria)
    } catch (error) {
      console.error('Error generating criteria:', error)
      setError('Failed to generate scoring criteria')
    }
  }

  const generateScoringCriteria = (job: Job, type: string, level: string) => {
    // AI-like generation of scoring criteria based on job and interview details
    const baseCriteria = {
      'Technical': {
        'L1': 'Basic technical knowledge, problem-solving approach, communication skills, cultural fit',
        'L2': 'Technical proficiency, coding skills, system design basics, debugging ability',
        'L3': 'Advanced technical skills, architecture knowledge, optimization techniques, mentoring potential',
        'L4': 'Technical leadership, strategic thinking, team collaboration, project management',
        'L5': 'Technical vision, innovation, cross-functional collaboration, business alignment',
        'L6': 'Executive presence, technical strategy, organizational impact, long-term vision'
      },
      'HR': {
        'L1': 'Communication skills, cultural alignment, motivation, basic qualifications',
        'L2': 'Behavioral competencies, team fit, career goals, conflict resolution',
        'L3': 'Leadership potential, adaptability, emotional intelligence, growth mindset',
        'L4': 'Management skills, strategic thinking, people development, change management',
        'L5': 'Executive presence, organizational leadership, culture building, stakeholder management',
        'L6': 'Visionary leadership, organizational transformation, executive decision-making, board readiness'
      },
      'Managerial': {
        'L1': 'Basic leadership understanding, team collaboration, project coordination, communication',
        'L2': 'Team management, delegation skills, performance management, conflict resolution',
        'L3': 'Strategic planning, cross-functional leadership, talent development, process improvement',
        'L4': 'Organizational leadership, change management, business acumen, stakeholder engagement',
        'L5': 'Executive leadership, strategic vision, organizational development, market understanding',
        'L6': 'C-level readiness, board interaction, industry leadership, transformational change'
      },
      'Final': {
        'L1': 'Overall fit assessment, final concerns resolution, compensation discussion, next steps',
        'L2': 'Comprehensive evaluation, reference validation, offer readiness, onboarding preparation',
        'L3': 'Senior role assessment, strategic alignment, long-term potential, integration planning',
        'L4': 'Leadership evaluation, organizational impact, cultural influence, succession planning',
        'L5': 'Executive assessment, board presentation readiness, market positioning, strategic value',
        'L6': 'C-suite evaluation, investor readiness, industry impact, transformational leadership'
      }
    }

    const criteria = baseCriteria[type as keyof typeof baseCriteria]?.[level as keyof typeof baseCriteria['Technical']] ||
                    'Technical competency, communication skills, cultural fit, problem-solving ability'

    return `${job.job_title} - ${type} Interview (${level})\n\nScoring Criteria:\n• ${criteria.split(', ').join('\n• ')}\n\nRating Scale: 1-5 (1=Poor, 2=Below Average, 3=Average, 4=Good, 5=Excellent)`
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setScheduling(true)
    setError('')

    if (!selectedJob) {
      setError('Please select a job')
      setScheduling(false)
      return
    }

    if (!selectedCandidate) {
      setError('Please select a candidate')
      setScheduling(false)
      return
    }

    try {
      const formData = new FormData()
      formData.append('title', `${interviewType} Interview - ${selectedCandidate.full_name}`)
      formData.append('job_id', selectedJob.id)
      formData.append('candidate_id', selectedCandidate.id)
      formData.append('interview_level', interviewLevel)
      formData.append('scheduled_date', scheduledDate)
      formData.append('scheduled_time', scheduledTime)
      formData.append('duration_minutes', duration)
      formData.append('interview_mode', interviewMode)
      formData.append('meeting_link', meetingLink)
      formData.append('meeting_room', meetingRoom)
      formData.append('panel_members', panelMembers)
      formData.append('interview_type', interviewType)
      formData.append('status', completionStatus)
      formData.append('result', interviewResult)
      formData.append('feedback', feedback)
      formData.append('scoring_criteria', scoringCriteria)
      formData.append('score', score)

      if (transcriptFile) {
        formData.append('transcript_file', transcriptFile)
      }

      const response = await fetch('http://localhost:8000/api/interviews/interviews/create/', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      const result = await response.json()

      if (result.success) {
        onInterviewScheduled()
        onClose()
      } else {
        setError(result.error || 'Failed to schedule interview')
      }
    } catch (error) {
      console.error('Error scheduling interview:', error)
      setError('Network error')
    } finally {
      setScheduling(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-card-foreground flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            Schedule Interview
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-md transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 p-4 bg-destructive/15 text-destructive rounded-md">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Job and Candidate Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Job Selection */}
              <div className="relative">
                <label htmlFor="jobSearch" className="block text-sm font-medium mb-1">
                  <Briefcase className="h-4 w-4 inline mr-1" />
                  Job (Ticket ID / Job Title) *
                </label>
                <div className="relative">
                  <input
                    id="jobSearch"
                    type="text"
                    value={jobSearch}
                    onChange={(e) => setJobSearch(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm pr-8"
                    placeholder="Search by ticket ID or job title..."
                    required
                  />
                  <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
                
                {/* Job Search Results */}
                {showJobResults && jobResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {jobResults.map((job) => (
                      <button
                        key={job.id}
                        type="button"
                        onClick={() => handleJobSelect(job)}
                        className="w-full text-left px-3 py-2 hover:bg-muted transition-colors border-b border-border last:border-b-0"
                      >
                        <div className="font-medium text-sm">{job.ticket_id} - {job.job_title}</div>
                        {job.job_role?.role_name && (
                          <div className="text-xs text-muted-foreground">{job.job_role.role_name}</div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Candidate Selection */}
              <div className="relative">
                <label htmlFor="candidateSearch" className="block text-sm font-medium mb-1">
                  <User className="h-4 w-4 inline mr-1" />
                  Candidate (Name / Candidate ID) *
                </label>
                <div className="relative">
                  <input
                    id="candidateSearch"
                    type="text"
                    value={candidateSearch}
                    onChange={(e) => setCandidateSearch(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm pr-8"
                    placeholder="Search by name or candidate ID..."
                    required
                  />
                  <Search className="absolute right-2 top-2.5 h-4 w-4 text-muted-foreground" />
                </div>
                
                {/* Candidate Search Results */}
                {showCandidateResults && candidateResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-background border border-border rounded-md shadow-lg max-h-60 overflow-y-auto">
                    {candidateResults.map((candidate) => (
                      <button
                        key={candidate.id}
                        type="button"
                        onClick={() => handleCandidateSelect(candidate)}
                        className="w-full text-left px-3 py-2 hover:bg-muted transition-colors border-b border-border last:border-b-0"
                      >
                        <div className="font-medium text-sm">{candidate.candidate_id} - {candidate.full_name}</div>
                        <div className="text-xs text-muted-foreground">{candidate.email}</div>
                        {candidate.preferred_role_name && (
                          <div className="text-xs text-muted-foreground">{candidate.preferred_role_name}</div>
                        )}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Interview Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="interviewLevel" className="block text-sm font-medium mb-1">
                  Interview Level *
                </label>
                <select
                  id="interviewLevel"
                  value={interviewLevel}
                  onChange={(e) => setInterviewLevel(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  required
                >
                  <option value="">Select level</option>
                  <option value="L1">L1 - Initial Screening</option>
                  <option value="L2">L2 - Technical Round</option>
                  <option value="L3">L3 - Advanced Technical</option>
                  <option value="L4">L4 - Managerial Round</option>
                  <option value="L5">L5 - HR Round</option>
                  <option value="L6">L6 - Final Round</option>
                </select>
              </div>

              <div>
                <label htmlFor="interviewType" className="block text-sm font-medium mb-1">
                  Interview Type *
                </label>
                <select
                  id="interviewType"
                  value={interviewType}
                  onChange={(e) => setInterviewType(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  required
                >
                  <option value="Technical">Technical</option>
                  <option value="HR">HR</option>
                  <option value="Managerial">Managerial</option>
                  <option value="Final">Final</option>
                </select>
              </div>

              <div>
                <label htmlFor="scheduledDate" className="block text-sm font-medium mb-1">
                  <Calendar className="h-4 w-4 inline mr-1" />
                  Scheduled Date *
                </label>
                <input
                  id="scheduledDate"
                  type="date"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  required
                />
              </div>

              <div>
                <label htmlFor="scheduledTime" className="block text-sm font-medium mb-1">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Scheduled Time *
                </label>
                <input
                  id="scheduledTime"
                  type="time"
                  value={scheduledTime}
                  onChange={(e) => setScheduledTime(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  required
                />
              </div>

              <div>
                <label htmlFor="duration" className="block text-sm font-medium mb-1">
                  Duration (minutes)
                </label>
                <input
                  id="duration"
                  type="number"
                  min="15"
                  max="240"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="60"
                />
              </div>

              <div>
                <label htmlFor="interviewMode" className="block text-sm font-medium mb-1">
                  Interview Mode *
                </label>
                <select
                  id="interviewMode"
                  value={interviewMode}
                  onChange={(e) => setInterviewMode(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  required
                >
                  <option value="Video Call">Video Call</option>
                  <option value="In-Person">In-Person</option>
                  <option value="Phone Call">Phone Call</option>
                </select>
              </div>
            </div>

            {/* Meeting Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="meetingLink" className="block text-sm font-medium mb-1">
                  <Video className="h-4 w-4 inline mr-1" />
                  Meeting Link
                </label>
                <input
                  id="meetingLink"
                  type="url"
                  value={meetingLink}
                  onChange={(e) => setMeetingLink(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="https://meet.google.com/..."
                />
              </div>

              <div>
                <label htmlFor="meetingRoom" className="block text-sm font-medium mb-1">
                  Meeting Room
                </label>
                <input
                  id="meetingRoom"
                  type="text"
                  value={meetingRoom}
                  onChange={(e) => setMeetingRoom(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="Conference Room A"
                />
              </div>
            </div>

            <div>
              <label htmlFor="panelMembers" className="block text-sm font-medium mb-1">
                <Users className="h-4 w-4 inline mr-1" />
                Panel Members
              </label>
              <textarea
                id="panelMembers"
                value={panelMembers}
                onChange={(e) => setPanelMembers(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                rows={2}
                placeholder="Enter panel member names, separated by commas"
              />
            </div>

            {/* Interview Results */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="completionStatus" className="block text-sm font-medium mb-1">
                  Completion Status
                </label>
                <select
                  id="completionStatus"
                  value={completionStatus}
                  onChange={(e) => setCompletionStatus(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="Scheduled">Scheduled</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Completed">Completed</option>
                  <option value="Cancelled">Cancelled</option>
                  <option value="Rescheduled">Rescheduled</option>
                </select>
              </div>

              <div>
                <label htmlFor="interviewResult" className="block text-sm font-medium mb-1">
                  Interview Result
                </label>
                <select
                  id="interviewResult"
                  value={interviewResult}
                  onChange={(e) => setInterviewResult(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                >
                  <option value="Pending">Pending</option>
                  <option value="Selected">Selected</option>
                  <option value="Rejected">Rejected</option>
                  <option value="On Hold">On Hold</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="scoringCriteria" className="block text-sm font-medium mb-1">
                  Scoring Criteria
                </label>
                <div className="space-y-2">
                  <textarea
                    id="scoringCriteria"
                    value={scoringCriteria}
                    onChange={(e) => setScoringCriteria(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    rows={4}
                    placeholder="Define the criteria for evaluating this interview..."
                  />
                  <button
                    type="button"
                    onClick={handleAIGenerateCriteria}
                    className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-secondary text-secondary-foreground hover:bg-secondary/80 h-8 px-3"
                  >
                    <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                    AI Generate
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="score" className="block text-sm font-medium mb-1">
                  Score
                </label>
                <input
                  id="score"
                  type="text"
                  value={score}
                  onChange={(e) => setScore(e.target.value)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  placeholder="e.g., 8/10, 85%, Excellent"
                />
              </div>

              <div>
                <label htmlFor="transcriptFile" className="block text-sm font-medium mb-1">
                  <FileText className="h-4 w-4 inline mr-1" />
                  Interview Transcript
                </label>
                <input
                  id="transcriptFile"
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleTranscriptFileChange}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                />
              </div>
            </div>

            <div>
              <label htmlFor="feedback" className="block text-sm font-medium mb-1">
                Feedback / Notes
              </label>
              <textarea
                id="feedback"
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                rows={3}
                placeholder="Enter interview feedback and notes..."
              />
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border">
              <button
                type="button"
                onClick={onClose}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={scheduling}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              >
                {scheduling ? 'Scheduling...' : 'Schedule Interview'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
