import { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Home, Users, Settings, LogOut, Menu, X, Building, Briefcase, UserCheck, Calendar, Send, TrendingUp } from 'lucide-react'
import UserManagement from './UserManagement'
import GroupManagement from './GroupManagement'
import VendorManagement from './VendorManagement'
import JobManagement from './JobManagement'
import CandidateManagement from './CandidateManagement'
import InterviewManagement from './InterviewManagement'
import JobDetailsView from './JobDetailsView'
import SystemSettings from './SystemSettings'
import BulkResumeEvaluation from './BulkResumeEvaluation'
import EditJobModal from './EditJobModal'

interface User {
  id: string
  email: string
  employee_id: string
  full_name: string
  user_type: string
  role_category: string
  can_access_admin: boolean
}

interface DashboardData {
  user: User
  permissions: {
    can_access_admin: boolean
    can_manage_users: boolean
    can_manage_admins: boolean
    can_view_admin_panel: boolean
    can_manage_vendors: boolean
  }
  stats: {
    total_users: number
    active_users: number
    total_admins: number
    total_vendors: number
  }
}

export default function Dashboard() {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [loading, setLoading] = useState(true)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [activeTab, setActiveTab] = useState('home')
  const [selectedJobId, setSelectedJobId] = useState<string | null>(null)
  const [bulkEvaluationJobId, setBulkEvaluationJobId] = useState<string | null>(null)
  const [jobsOverTime, setJobsOverTime] = useState<{date: string, count: number}[]>([])
  const [jobsByTitle, setJobsByTitle] = useState<{title: string, count: number}[]>([])
  const [chartLoading, setChartLoading] = useState(false)
  const [titleChartLoading, setTitleChartLoading] = useState(false)
  const [selectedTimePeriod, setSelectedTimePeriod] = useState('30')
  const [hoveredPoint, setHoveredPoint] = useState<{index: number, x: number, y: number} | null>(null)
  const [hoveredPieSlice, setHoveredPieSlice] = useState<number | null>(null)
  const [showEditJobModal, setShowEditJobModal] = useState(false)
  const [editingJobId, setEditingJobId] = useState<string | null>(null)
  const navigate = useNavigate()

  useEffect(() => {
    fetchDashboardData()
    fetchJobsOverTime()
    fetchJobsByTitle()
  }, [])

  // Separate useEffect for navigation state restoration to ensure it runs after component mount
  useEffect(() => {
    // Add a small delay to ensure the component is fully mounted
    const timer = setTimeout(() => {
      try {
        const savedState = localStorage.getItem('dashboard-navigation-state')
        console.log('🔄 Attempting to restore navigation state:', savedState)

        if (savedState) {
          const { activeTab: savedActiveTab, selectedJobId: savedJobId, bulkEvaluationJobId: savedBulkJobId } = JSON.parse(savedState)
          console.log('📋 Parsed saved state:', { savedActiveTab, savedJobId, savedBulkJobId })

          if (savedActiveTab && savedActiveTab !== 'home') {
            console.log('🎯 Restoring activeTab to:', savedActiveTab)
            setActiveTab(savedActiveTab)
          }
          if (savedJobId) {
            console.log('🎯 Restoring selectedJobId to:', savedJobId)
            setSelectedJobId(savedJobId)
          }
          if (savedBulkJobId) {
            console.log('🎯 Restoring bulkEvaluationJobId to:', savedBulkJobId)
            setBulkEvaluationJobId(savedBulkJobId)
          }
        } else {
          console.log('❌ No saved navigation state found')
        }
      } catch (error) {
        console.error('❌ Error restoring navigation state:', error)
      }
    }, 100) // Small delay to ensure component is ready

    return () => clearTimeout(timer)
  }, []) // Run only once on mount

  // Save navigation state to localStorage whenever it changes
  useEffect(() => {
    try {
      const navigationState = {
        activeTab,
        selectedJobId,
        bulkEvaluationJobId
      }
      console.log('💾 Saving navigation state:', navigationState)
      localStorage.setItem('dashboard-navigation-state', JSON.stringify(navigationState))
    } catch (error) {
      console.error('❌ Error saving navigation state:', error)
    }
  }, [activeTab, selectedJobId, bulkEvaluationJobId])

  const handleTimePeriodChange = (period: string) => {
    setSelectedTimePeriod(period)
    fetchJobsOverTime(period)
  }

  // Helper function to navigate to a tab and clear job-specific states if needed
  const navigateToTab = (tabId: string) => {
    setActiveTab(tabId)

    // Clear job-specific states when navigating away from job-related views
    if (tabId !== 'job-details' && tabId !== 'bulk-evaluation') {
      setSelectedJobId(null)
      setBulkEvaluationJobId(null)
    }
  }

  // Function to clear navigation state (useful for logout or explicit home navigation)
  const clearNavigationState = () => {
    try {
      localStorage.removeItem('dashboard-navigation-state')
    } catch (error) {
      console.error('Error clearing navigation state:', error)
    }
  }

  const fetchJobsOverTime = async (days: string = selectedTimePeriod) => {
    setChartLoading(true)
    try {
      const url = days === 'all'
        ? 'http://localhost:8000/api/job-management/jobs/over-time/?days=365'
        : `http://localhost:8000/api/job-management/jobs/over-time/?days=${days}`
      const response = await fetch(url, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobsOverTime(data.data)
        }
      }
    } catch (error) {
      console.error('Error fetching jobs over time:', error)
    } finally {
      setChartLoading(false)
    }
  }

  const fetchJobsByTitle = async () => {
    setTitleChartLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/job-management/jobs/by-title/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJobsByTitle(data.data)
        }
      }
    } catch (error) {
      console.error('Error fetching jobs by title:', error)
    } finally {
      setTitleChartLoading(false)
    }
  }

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/core/dashboard/', {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setDashboardData(data.data)
        } else {
          navigate('/bce')
        }
      } else {
        navigate('/bce')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      navigate('/bce')
    } finally {
      setLoading(false)
    }
  }

  const handleJobSelect = (jobId: string) => {
    setSelectedJobId(jobId)
    setActiveTab('job-details')
  }

  const handleBackToJobs = () => {
    setSelectedJobId(null)
    setActiveTab('jobs')
  }

  const handleLogout = async () => {
    try {
      // Clear navigation state before logout
      clearNavigationState()

      await fetch('http://localhost:8000/api/auth/logout/', {
        method: 'POST',
        credentials: 'include',
      })
      navigate('/bce')
    } catch (error) {
      console.error('Error logging out:', error)
      navigate('/bce')
    }
  }

  const handleBulkEvaluation = (jobId: string) => {
    setBulkEvaluationJobId(jobId)
    setActiveTab('bulk-evaluation')
  }

  const handleEditJob = (jobId: string) => {
    setEditingJobId(jobId)
    setShowEditJobModal(true)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Failed to load dashboard</p>
          <button
            onClick={() => navigate('/bce')}
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
          >
            Back to Login
          </button>
        </div>
      </div>
    )
  }

  const sidebarItems = [
    { id: 'home', label: 'Home', icon: Home },
    ...(dashboardData.permissions.can_access_admin ? [
      { id: 'jobs', label: 'Job Management', icon: Briefcase },
      { id: 'candidates', label: 'Candidate Management', icon: UserCheck },
      { id: 'interviews', label: 'Interviews', icon: Calendar },
      { id: 'vendors', label: 'Vendor Management', icon: Building },
      { id: 'campaigns', label: 'Outbound Campaigns', icon: Send },
      { id: 'admin', label: 'Admin Panel', icon: Settings }
    ] : []),
  ]

  return (
    <div className="min-h-screen bg-background flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-16'} bg-card border-r border-border transition-all duration-300 flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            {sidebarOpen && (
              <h1 className="text-xl font-bold text-card-foreground">Talent Hero</h1>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md hover:bg-muted transition-colors"
            >
              {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => {
              const Icon = item.icon
              return (
                <li key={item.id}>
                  <button
                    onClick={() => navigateToTab(item.id)}
                    className={`w-full flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      activeTab === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {sidebarOpen && <span className="ml-3">{item.label}</span>}
                  </button>
                </li>
              )
            })}
          </ul>
        </nav>

        {/* User Info & Logout */}
        <div className="p-4 border-t border-border">
          {sidebarOpen && (
            <div className="mb-3">
              <p className="text-sm font-medium text-card-foreground">{dashboardData.user.full_name}</p>
              <p className="text-xs text-muted-foreground">{dashboardData.user.employee_id}</p>
            </div>
          )}
          <button
            onClick={handleLogout}
            className="w-full flex items-center px-3 py-2 rounded-md text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
          >
            <LogOut className="h-4 w-4" />
            {sidebarOpen && <span className="ml-3">Logout</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className="bg-card border-b border-border p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-card-foreground">
                {activeTab === 'home' ? 'Dashboard' :
                 activeTab === 'admin' ? 'Admin Panel' :
                 activeTab === 'users' ? 'User Management' :
                 activeTab === 'groups' ? 'Group Management' :
                 activeTab === 'vendors' ? 'Vendor Management' :
                 activeTab === 'jobs' ? 'Job Management' :
                 activeTab === 'bulk-evaluation' ? 'Bulk Resume Evaluation' :
                 activeTab === 'candidates' ? 'Candidate Management' :
                 activeTab === 'interviews' ? 'Interviews' :
                 activeTab === 'campaigns' ? 'Outbound Campaigns' : 'Dashboard'}
              </h2>
              <p className="text-muted-foreground">
                Welcome back, {dashboardData.user.full_name}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-muted-foreground">
                {dashboardData.user.department} • {dashboardData.user.position}
              </span>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="flex-1 p-6">
          {activeTab === 'home' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {dashboardData.permissions.can_access_admin && (
                  <>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Total Users</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_users}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Active Users</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.active_users}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Admins</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_admins}</p>
                    </div>
                    <div className="bg-card rounded-lg border border-border p-6">
                      <h3 className="text-sm font-medium text-muted-foreground">Vendors</h3>
                      <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_vendors}</p>
                    </div>
                  </>
                )}
              </div>

              {/* Additional Metrics Row */}
              {dashboardData.permissions.can_access_admin && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-sm font-medium text-muted-foreground">Job Roles</h3>
                    <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_job_roles || 0}</p>
                  </div>
                  <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-sm font-medium text-muted-foreground">Jobs</h3>
                    <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_jobs || 0}</p>
                  </div>
                  <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-sm font-medium text-muted-foreground">Candidates</h3>
                    <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_candidates || 0}</p>
                  </div>
                  <div className="bg-card rounded-lg border border-border p-6">
                    <h3 className="text-sm font-medium text-muted-foreground">Interviews</h3>
                    <p className="text-2xl font-bold text-card-foreground">{dashboardData.stats.total_interviews || 0}</p>
                  </div>
                </div>
              )}

              {/* Charts Section */}
              {dashboardData.permissions.can_access_admin && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Jobs Over Time Chart */}
                  <div className="bg-card rounded-lg border border-border p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-card-foreground flex items-center">
                      <TrendingUp className="h-5 w-5 mr-2" />
                      Jobs Posted Over Time
                    </h3>
                    <div className="flex items-center space-x-2">
                      {[
                        { label: '1W', value: '7' },
                        { label: '1M', value: '30' },
                        { label: '3M', value: '90' },
                        { label: 'All', value: 'all' }
                      ].map((period) => (
                        <button
                          key={period.value}
                          onClick={() => handleTimePeriodChange(period.value)}
                          className={`px-3 py-1 text-xs rounded-md transition-colors ${
                            selectedTimePeriod === period.value
                              ? 'bg-primary text-primary-foreground'
                              : 'bg-muted text-muted-foreground hover:bg-muted/80'
                          }`}
                        >
                          {period.label}
                        </button>
                      ))}
                    </div>
                  </div>

                  {chartLoading ? (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-muted-foreground">Loading chart...</div>
                    </div>
                  ) : (
                    <div className="h-80 w-full relative">
                      <svg viewBox="0 0 800 250" className="w-full h-full">
                        {/* Chart background */}
                        <rect width="800" height="250" fill="transparent" />

                        {/* Gradient definition for line */}
                        <defs>
                          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
                            <stop offset="50%" stopColor="#1d4ed8" stopOpacity="1" />
                            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.8" />
                          </linearGradient>
                          <filter id="lineShadow">
                            <feDropShadow dx="0" dy="1" stdDeviation="1" floodColor="#3b82f6" floodOpacity="0.3"/>
                          </filter>
                        </defs>

                        {/* Grid lines */}
                        {[0, 1, 2, 3, 4, 5].map(i => (
                          <line
                            key={`grid-${i}`}
                            x1="60"
                            y1={40 + i * 24}
                            x2="760"
                            y2={40 + i * 24}
                            stroke="currentColor"
                            strokeOpacity="0.1"
                            className="text-muted-foreground"
                          />
                        ))}

                        {/* Y-axis */}
                        <line x1="60" y1="40" x2="60" y2="184" stroke="currentColor" strokeOpacity="0.3" className="text-muted-foreground" />

                        {/* X-axis */}
                        <line x1="60" y1="184" x2="760" y2="184" stroke="currentColor" strokeOpacity="0.3" className="text-muted-foreground" />

                        {/* Chart line - connect all points */}
                        {jobsOverTime.length > 1 && (
                          <>
                            {/* Shadow line for depth */}
                            <polyline
                              fill="none"
                              stroke="#3b82f6"
                              strokeWidth="5"
                              strokeOpacity="0.2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              points={jobsOverTime.map((point, index) => {
                                const x = 60 + (index / Math.max(jobsOverTime.length - 1, 1)) * 700
                                const maxCount = Math.max(...jobsOverTime.map(p => p.count), 1)
                                const y = 186 - (point.count / maxCount) * 144  // Slightly offset for shadow
                                return `${x},${y}`
                              }).join(' ')}
                            />
                            {/* Main line */}
                            <polyline
                              fill="none"
                              stroke="url(#lineGradient)"
                              strokeWidth="3"
                              strokeOpacity="1"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              filter="url(#lineShadow)"
                              points={jobsOverTime.map((point, index) => {
                                const x = 60 + (index / Math.max(jobsOverTime.length - 1, 1)) * 700
                                const maxCount = Math.max(...jobsOverTime.map(p => p.count), 1)
                                const y = 184 - (point.count / maxCount) * 144
                                return `${x},${y}`
                              }).join(' ')}
                            />
                          </>
                        )}

                        {/* Data points - interactive with hover */}
                        {jobsOverTime.map((point, index) => {
                          const x = 60 + (index / Math.max(jobsOverTime.length - 1, 1)) * 700
                          const maxCount = Math.max(...jobsOverTime.map(p => p.count), 1)
                          const y = 184 - (point.count / maxCount) * 144
                          const isHovered = hoveredPoint?.index === index

                          return (
                            <g key={`point-${index}`}>
                              {/* Invisible larger circle for easier hovering */}
                              <circle
                                cx={x}
                                cy={y}
                                r="12"
                                fill="transparent"
                                style={{ cursor: 'pointer' }}
                                onMouseEnter={() => setHoveredPoint({ index, x, y })}
                                onMouseLeave={() => setHoveredPoint(null)}
                              />
                              {/* Visible point */}
                              <circle
                                cx={x}
                                cy={y}
                                r={isHovered ? "7" : "5"}
                                fill="#3b82f6"
                                stroke="white"
                                strokeWidth="3"
                                style={{
                                  cursor: 'pointer',
                                  filter: isHovered ? 'brightness(1.3) drop-shadow(0 0 8px #3b82f6)' : 'drop-shadow(0 1px 3px rgba(0,0,0,0.2))',
                                  transition: 'all 0.2s ease'
                                }}
                                onMouseEnter={() => setHoveredPoint({ index, x, y })}
                                onMouseLeave={() => setHoveredPoint(null)}
                              />
                            </g>
                          )
                        })}

                        {/* Y-axis labels */}
                        {[0, 1, 2, 3, 4, 5].map(i => {
                          const maxCount = Math.max(...jobsOverTime.map(p => p.count), 5)
                          const value = Math.round((maxCount / 5) * (5 - i))
                          return (
                            <text
                              key={`y-label-${i}`}
                              x="50"
                              y={44 + i * 24}
                              textAnchor="end"
                              className="text-xs fill-current text-muted-foreground"
                            >
                              {value}
                            </text>
                          )
                        })}
                      </svg>

                      {jobsOverTime.length === 0 && (
                        <div className="absolute inset-0 flex items-center justify-center text-muted-foreground">
                          No job data available for the selected period
                        </div>
                      )}

                      {/* Hover tooltip */}
                      {hoveredPoint && (
                        <div
                          className="absolute bg-card border border-border rounded-lg shadow-lg p-3 pointer-events-none z-10"
                          style={{
                            left: `${(hoveredPoint.x / 800) * 100}%`,
                            top: `${(hoveredPoint.y / 250) * 100}%`,
                            transform: 'translate(-50%, -120%)'
                          }}
                        >
                          <div className="text-sm font-medium text-card-foreground">
                            {jobsOverTime[hoveredPoint.index]?.count} jobs
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(jobsOverTime[hoveredPoint.index]?.date).toLocaleDateString('en-US', {
                              weekday: 'short',
                              month: 'short',
                              day: 'numeric',
                              year: 'numeric'
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                  </div>

                  {/* Jobs by Title Pie Chart */}
                  <div className="bg-card rounded-lg border border-border p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-card-foreground flex items-center">
                      <Briefcase className="h-5 w-5 mr-2" />
                      Jobs by Title (Top 6)
                    </h3>
                  </div>

                  {titleChartLoading ? (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-muted-foreground">Loading chart...</div>
                    </div>
                  ) : (
                    <div className="h-96 w-full flex items-center justify-center">
                      {jobsByTitle.length > 0 ? (
                        (() => {
                          // Process data for pie chart - top 6 + others
                          const top6 = jobsByTitle.slice(0, 6)
                          const others = jobsByTitle.slice(6)
                          const othersCount = others.reduce((sum, job) => sum + job.count, 0)

                          const pieData = [...top6]
                          if (othersCount > 0) {
                            pieData.push({ title: 'Others', count: othersCount })
                          }

                          const total = pieData.reduce((sum, item) => sum + item.count, 0)
                          const colors = [
                            '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
                            '#8b5cf6', '#06b6d4', '#6b7280'
                          ]

                          let currentAngle = 0
                          const centerX = 200
                          const centerY = 150
                          const radius = 80
                          const depth = 20

                          return (
                            <div className="flex items-center space-x-8 relative">
                              <svg viewBox="0 0 400 300" className="w-80 h-60">
                                {/* 3D effect - bottom ellipse */}
                                <ellipse
                                  cx={centerX}
                                  cy={centerY + depth}
                                  rx={radius}
                                  ry={radius * 0.3}
                                  fill="rgba(0,0,0,0.2)"
                                />

                                {/* Pie slices */}
                                {pieData.map((item, index) => {
                                  const percentage = (item.count / total) * 100
                                  const angle = (item.count / total) * 360
                                  const startAngle = currentAngle
                                  const endAngle = currentAngle + angle
                                  const isHovered = hoveredPieSlice === index

                                  const currentRadius = isHovered ? radius + 5 : radius
                                  const startX = centerX + currentRadius * Math.cos((startAngle - 90) * Math.PI / 180)
                                  const startY = centerY + currentRadius * Math.sin((startAngle - 90) * Math.PI / 180)
                                  const endX = centerX + currentRadius * Math.cos((endAngle - 90) * Math.PI / 180)
                                  const endY = centerY + currentRadius * Math.sin((endAngle - 90) * Math.PI / 180)

                                  const largeArcFlag = angle > 180 ? 1 : 0

                                  const pathData = [
                                    `M ${centerX} ${centerY}`,
                                    `L ${startX} ${startY}`,
                                    `A ${currentRadius} ${currentRadius} 0 ${largeArcFlag} 1 ${endX} ${endY}`,
                                    'Z'
                                  ].join(' ')

                                  currentAngle += angle

                                  return (
                                    <g key={index}>
                                      {/* 3D side effect */}
                                      <path
                                        d={pathData.replace(new RegExp(centerY.toString(), 'g'), (centerY + depth).toString())}
                                        fill={colors[index % colors.length]}
                                        opacity="0.6"
                                      />
                                      {/* Top slice */}
                                      <path
                                        d={pathData}
                                        fill={colors[index % colors.length]}
                                        stroke="white"
                                        strokeWidth="2"
                                        style={{
                                          cursor: 'pointer',
                                          filter: isHovered ? 'brightness(1.1)' : 'none',
                                          transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={() => setHoveredPieSlice(index)}
                                        onMouseLeave={() => setHoveredPieSlice(null)}
                                      />
                                    </g>
                                  )
                                })}
                              </svg>

                              {/* Legend */}
                              <div className="space-y-2">
                                {pieData.map((item, index) => (
                                  <div key={index} className="flex items-center space-x-2">
                                    <div
                                      className="w-4 h-4 rounded"
                                      style={{ backgroundColor: colors[index % colors.length] }}
                                    />
                                    <div className="text-sm">
                                      <div className="font-medium text-card-foreground truncate max-w-32" title={item.title}>
                                        {item.title}
                                      </div>
                                      <div className="text-muted-foreground">
                                        {item.count} jobs
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>

                              {/* Hover tooltip for pie chart */}
                              {hoveredPieSlice !== null && (
                                <div className="absolute bg-card border border-border rounded-lg shadow-lg p-3 pointer-events-none z-10 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                  <div className="text-sm font-medium text-card-foreground">
                                    {pieData[hoveredPieSlice]?.title}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {pieData[hoveredPieSlice]?.count} jobs ({((pieData[hoveredPieSlice]?.count / total) * 100).toFixed(1)}%)
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        })()
                      ) : (
                        <div className="flex items-center justify-center h-64 text-muted-foreground">
                          No job title data available
                        </div>
                      )}
                    </div>
                  )}
                  </div>
                </div>
              )}

              <div className="bg-card rounded-lg border border-border p-6">
                <h3 className="text-lg font-semibold text-card-foreground mb-4">
                  Talent Hero v3.11
                </h3>
                <div className="space-y-3">
                  <p className="text-muted-foreground">
                    Your comprehensive talent acquisition and management platform. Use the sidebar to navigate through different sections.
                  </p>
                  <div className="bg-muted/50 rounded-md p-4">
                    <h4 className="text-sm font-medium text-card-foreground mb-2">Your Profile</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-muted-foreground">Username:</span>
                        <span className="ml-2 font-mono text-card-foreground">{dashboardData.user.email.split('@')[0]}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Employee ID:</span>
                        <span className="ml-2 font-mono text-card-foreground">{dashboardData.user.employee_id}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Role:</span>
                        <span className="ml-2 text-card-foreground">{dashboardData.user.user_type}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Department:</span>
                        <span className="ml-2 text-card-foreground">{dashboardData.user.department}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'admin' && dashboardData.permissions.can_access_admin && (
            <div className="space-y-6">
              <div className="bg-card rounded-lg border border-border p-6">
                <p className="text-muted-foreground mb-6">
                  Manage users, permissions, and system settings.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <button
                    onClick={() => navigateToTab('users')}
                    className="block p-4 rounded-lg border border-border hover:bg-muted transition-colors text-left"
                  >
                    <Users className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium text-card-foreground">User Management</h4>
                    <p className="text-sm text-muted-foreground">Create, edit, and manage user accounts</p>
                  </button>

                  <button
                    onClick={() => navigateToTab('groups')}
                    className="block p-4 rounded-lg border border-border hover:bg-muted transition-colors text-left"
                  >
                    <Settings className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium text-card-foreground">Groups & Permissions</h4>
                    <p className="text-sm text-muted-foreground">Manage user groups and permissions</p>
                  </button>

                  <button
                    onClick={() => navigateToTab('settings')}
                    className="block p-4 rounded-lg border border-border hover:bg-muted transition-colors text-left"
                  >
                    <Settings className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium text-card-foreground">System Settings</h4>
                    <p className="text-sm text-muted-foreground">Configure system preferences and settings</p>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'users' && dashboardData.permissions.can_access_admin && (
            <UserManagement onBack={() => setActiveTab('admin')} />
          )}

          {activeTab === 'groups' && dashboardData.permissions.can_access_admin && (
            <GroupManagement onBack={() => setActiveTab('admin')} />
          )}

          {activeTab === 'settings' && dashboardData.permissions.can_access_admin && (
            <SystemSettings onBack={() => setActiveTab('admin')} />
          )}

          {activeTab === 'vendors' && dashboardData.permissions.can_access_admin && (
            <VendorManagement />
          )}

          {activeTab === 'jobs' && dashboardData.permissions.can_access_admin && (
            <JobManagement onJobSelect={handleJobSelect} />
          )}

          {activeTab === 'job-details' && selectedJobId && dashboardData.permissions.can_access_admin && (
            <JobDetailsView
              jobId={selectedJobId}
              onBack={handleBackToJobs}
              onBulkEvaluation={handleBulkEvaluation}
              onEditJob={handleEditJob}
            />
          )}

          {activeTab === 'bulk-evaluation' && bulkEvaluationJobId && dashboardData.permissions.can_access_admin && (
            <BulkResumeEvaluation
              jobId={bulkEvaluationJobId}
              onBack={() => {
                setSelectedJobId(bulkEvaluationJobId)
                setActiveTab('job-details')
                setBulkEvaluationJobId(null)
              }}
            />
          )}

          {activeTab === 'candidates' && dashboardData.permissions.can_access_admin && (
            <CandidateManagement />
          )}

          {activeTab === 'interviews' && dashboardData.permissions.can_access_admin && (
            <InterviewManagement />
          )}

          {activeTab === 'campaigns' && dashboardData.permissions.can_access_admin && (
            <div className="container mx-auto py-6 space-y-6">
              <div className="flex items-center">
                <div>
                  <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                    <Send className="h-6 w-6 mr-2" />
                    Outbound Campaigns
                  </h2>
                  <p className="text-muted-foreground">Manage outbound recruitment campaigns</p>
                </div>
              </div>
              <div className="bg-card rounded-lg border border-border p-8 text-center">
                <Send className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium text-card-foreground mb-2">Outbound Campaigns</h3>
                <p className="text-muted-foreground">Campaign management functionality will be implemented here.</p>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Edit Job Modal */}
      {editingJobId && (
        <EditJobModal
          isOpen={showEditJobModal}
          onClose={() => {
            setShowEditJobModal(false)
            setEditingJobId(null)
          }}
          onJobUpdated={() => {
            // Refresh job data if needed
            // You might want to add a callback to refresh the job details
          }}
          jobId={editingJobId}
        />
      )}
    </div>
  )
}
