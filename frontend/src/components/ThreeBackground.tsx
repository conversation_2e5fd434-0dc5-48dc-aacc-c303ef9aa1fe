import { useRef, useMemo, Suspense } from 'react'
import { Canvas, useFrame } from '@react-three/fiber'
import * as THREE from 'three'

// Floating particles component
function FloatingParticles() {
  const ref = useRef<THREE.Points>(null!)
  const particlesPosition = useMemo(() => {
    const positions = new Float32Array(1000 * 3)
    for (let i = 0; i < 1000; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 20
      positions[i * 3 + 1] = (Math.random() - 0.5) * 20
      positions[i * 3 + 2] = (Math.random() - 0.5) * 20
    }
    return positions
  }, [])

  useFrame((state) => {
    if (ref.current) {
      ref.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.1) * 0.1
      ref.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.15) * 0.1
      ref.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.05) * 0.05
    }
  })

  return (
    <points ref={ref}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particlesPosition.length / 3}
          array={particlesPosition}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        color="#8b5cf6"
        size={0.05}
        sizeAttenuation={true}
        transparent
        opacity={0.8}
      />
    </points>
  )
}

// Animated geometric shapes
function AnimatedShapes() {
  const meshRef = useRef<THREE.Group>(null!)
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = Math.sin(state.clock.elapsedTime * 0.2) * 0.1
      meshRef.current.rotation.y = state.clock.elapsedTime * 0.1
      meshRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 0.1) * 0.05
    }
  })

  return (
    <group ref={meshRef}>
      {/* Wireframe torus */}
      <mesh position={[-4, 2, -3]}>
        <torusGeometry args={[1.5, 0.4, 16, 100]} />
        <meshBasicMaterial color="#06b6d4" wireframe transparent opacity={0.8} />
      </mesh>

      {/* Wireframe octahedron */}
      <mesh position={[4, -2, -2]}>
        <octahedronGeometry args={[2]} />
        <meshBasicMaterial color="#10b981" wireframe transparent opacity={0.8} />
      </mesh>

      {/* Wireframe icosahedron */}
      <mesh position={[0, 3, -4]}>
        <icosahedronGeometry args={[1.2]} />
        <meshBasicMaterial color="#f59e0b" wireframe transparent opacity={0.8} />
      </mesh>

      {/* Wireframe dodecahedron */}
      <mesh position={[-3, -3, -3]}>
        <dodecahedronGeometry args={[1]} />
        <meshBasicMaterial color="#ef4444" wireframe transparent opacity={0.8} />
      </mesh>
    </group>
  )
}

// Floating rings
function FloatingRings() {
  const groupRef = useRef<THREE.Group>(null!)
  
  useFrame((state) => {
    if (groupRef.current) {
      groupRef.current.children.forEach((child, index) => {
        child.rotation.x = Math.sin(state.clock.elapsedTime * 0.3 + index) * 0.2
        child.rotation.y = Math.cos(state.clock.elapsedTime * 0.2 + index) * 0.3
        child.position.y = Math.sin(state.clock.elapsedTime * 0.4 + index * 2) * 0.5
      })
    }
  })

  return (
    <group ref={groupRef}>
      {Array.from({ length: 5 }, (_, i) => (
        <mesh key={i} position={[
          Math.cos(i * 1.26) * 4,
          0,
          Math.sin(i * 1.26) * 4 - 6
        ]}>
          <torusGeometry args={[1, 0.15, 8, 32]} />
          <meshBasicMaterial
            color={`hsl(${240 + i * 30}, 80%, 70%)`}
            transparent
            opacity={0.9}
          />
        </mesh>
      ))}
    </group>
  )
}

// Main 3D background component
export default function ThreeBackground() {
  return (
    <div className="fixed inset-0 -z-10">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        style={{ background: 'transparent' }}
        gl={{ antialias: true, alpha: true }}
      >
        <Suspense fallback={null}>
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} />

          <FloatingParticles />
          <AnimatedShapes />
          <FloatingRings />
        </Suspense>
      </Canvas>
    </div>
  )
}
