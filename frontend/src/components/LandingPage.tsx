import { Link } from 'react-router-dom'
import ThreeBackground from './ThreeBackground'

export default function LandingPage() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-background relative overflow-hidden">
      <ThreeBackground />
      <div className="text-center space-y-8 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Main content with glassmorphism effect */}
        <div className="backdrop-blur-sm bg-background/20 border border-white/10 rounded-3xl p-8 sm:p-12 shadow-2xl">
          <div className="space-y-6">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-foreground tracking-tight">
              Talent Hero
            </h1>
            <p className="text-lg sm:text-xl text-muted-foreground font-mono">
              v3.11
            </p>
          </div>

          <div className="space-y-6 mt-8">
            <p className="text-base sm:text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              An internal tool designed to streamline Talent Acquisition and Talent Management processes within organizations.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-8">
            <Link
              to="/vendor"
              className="w-full sm:w-auto inline-flex items-center justify-center rounded-xl bg-purple-600 hover:bg-purple-700 px-8 py-4 text-sm font-medium text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-500 focus-visible:ring-offset-2"
            >
              Vendor Login
            </Link>
            <Link
              to="/bce"
              className="w-full sm:w-auto inline-flex items-center justify-center rounded-xl bg-cyan-600 hover:bg-cyan-700 px-8 py-4 text-sm font-medium text-white shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-cyan-500 focus-visible:ring-offset-2"
            >
              BCE Internal Login
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
