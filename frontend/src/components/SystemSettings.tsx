import { useState, useEffect } from 'react'
import { ArrowLeft, Settings, Save, RefreshCw, Database, Mail, Shield, Globe, Brain, FolderOpen, Workflow, Calendar } from 'lucide-react'
import { API_BASE_URL } from '../config/api'

interface SystemSettingsProps {
  onBack: () => void
}

export default function SystemSettings({ onBack }: SystemSettingsProps) {
  const [settings, setSettings] = useState({
    // General Settings
    systemName: 'Talent Hero v3.11',
    systemDescription: 'Comprehensive talent acquisition and management platform',
    timezone: 'UTC',
    dateFormat: 'YYYY-MM-DD',
    
    // Security Settings
    sessionTimeout: '30',
    passwordMinLength: '8',
    requireSpecialChars: true,
    enableTwoFactor: false,

    // Notification Settings
    enableEmailNotifications: false,
    enableJobAlerts: false,
    enableCandidateAlerts: false,
    enableInterviewReminders: false,

    // AI Settings
    llmOperator: 'Ollama',
    apiKey: '',
    llmUrl: 'http://localhost:11434',
    selectedModel: '',
    availableModels: [],

    // Data Management Settings
    dataRetentionDays: '365',
    enableDataBackup: true,
    backupFrequency: 'daily',

    // Workflow Settings
    enableWorkflowAutomation: true,
    defaultWorkflowTimeout: '24',

    // Scheduling Settings
    defaultMeetingDuration: '60',
    workingHoursStart: '09:00',
    workingHoursEnd: '18:00'
  })

  const [saving, setSaving] = useState(false)
  const [activeSection, setActiveSection] = useState('general')
  const [activeAITab, setActiveAITab] = useState('selection')
  const [testingConnection, setTestingConnection] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      // Load AI settings
      const aiResponse = await fetch(`${API_BASE_URL}/api/settings/ai-settings/`, {
        credentials: 'include',
      })

      // Load system settings
      const systemResponse = await fetch(`${API_BASE_URL}/api/settings/system-settings/`, {
        credentials: 'include',
      })

      if (aiResponse.ok && systemResponse.ok) {
        const aiData = await aiResponse.json()
        const systemData = await systemResponse.json()

        if (aiData.success && systemData.success) {
          // Handle new multi-provider structure
          const activeProvider = aiData.data.active_provider || 'Ollama'
          const providers = aiData.data.providers || {}
          const activeProviderConfig = providers[activeProvider] || {}

          setSettings(prev => ({
            ...prev,
            // AI Settings (using active provider)
            llmOperator: activeProvider,
            apiKey: activeProviderConfig.api_key === '***' ? '' : (activeProviderConfig.api_key || ''),
            llmUrl: activeProviderConfig.llm_url || 'http://localhost:11434',
            selectedModel: activeProviderConfig.selected_model || '',
            availableModels: activeProviderConfig.available_models || [],
            // System Settings
            systemName: systemData.data.system_name,
            systemDescription: systemData.data.system_description,
            timezone: systemData.data.timezone,
            dateFormat: systemData.data.date_format,
            sessionTimeout: systemData.data.session_timeout.toString(),
            passwordMinLength: systemData.data.password_min_length.toString(),
            requireSpecialChars: systemData.data.require_special_chars,
            enableTwoFactor: systemData.data.enable_two_factor,
            enableEmailNotifications: systemData.data.enable_email_notifications,
            enableJobAlerts: systemData.data.enable_job_alerts,
            enableCandidateAlerts: systemData.data.enable_candidate_alerts,
            enableInterviewReminders: systemData.data.enable_interview_reminders,
            dataRetentionDays: systemData.data.data_retention_days.toString(),
            enableDataBackup: systemData.data.enable_data_backup,
            backupFrequency: systemData.data.backup_frequency,
            enableWorkflowAutomation: systemData.data.enable_workflow_automation,
            defaultWorkflowTimeout: systemData.data.default_workflow_timeout.toString(),
            defaultMeetingDuration: systemData.data.default_meeting_duration.toString(),
            workingHoursStart: systemData.data.working_hours_start,
            workingHoursEnd: systemData.data.working_hours_end
          }))
        }
      }
    } catch (error) {
      console.error('Failed to load settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Save AI settings
      const aiResponse = await fetch(`${API_BASE_URL}/api/settings/ai-settings/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          active_provider: settings.llmOperator,
          api_key: settings.apiKey,
          llm_url: settings.llmUrl,
          selected_model: settings.selectedModel,
          available_models: settings.availableModels
        })
      })

      // Save system settings
      const systemResponse = await fetch('http://localhost:8000/api/settings/system-settings/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          system_name: settings.systemName,
          system_description: settings.systemDescription,
          timezone: settings.timezone,
          date_format: settings.dateFormat,
          session_timeout: parseInt(settings.sessionTimeout),
          password_min_length: parseInt(settings.passwordMinLength),
          require_special_chars: settings.requireSpecialChars,
          enable_two_factor: settings.enableTwoFactor,
          enable_email_notifications: settings.enableEmailNotifications,
          enable_job_alerts: settings.enableJobAlerts,
          enable_candidate_alerts: settings.enableCandidateAlerts,
          enable_interview_reminders: settings.enableInterviewReminders,
          data_retention_days: parseInt(settings.dataRetentionDays),
          enable_data_backup: settings.enableDataBackup,
          backup_frequency: settings.backupFrequency,
          enable_workflow_automation: settings.enableWorkflowAutomation,
          default_workflow_timeout: parseInt(settings.defaultWorkflowTimeout),
          default_meeting_duration: parseInt(settings.defaultMeetingDuration),
          working_hours_start: settings.workingHoursStart,
          working_hours_end: settings.workingHoursEnd
        })
      })

      if (aiResponse.ok && systemResponse.ok) {
        alert('Settings saved successfully!')
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      alert('Failed to save settings')
      console.error('Save error:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))

    // Auto-save when model is selected
    if (key === 'selectedModel' && value) {
      setTimeout(() => {
        handleSave()
      }, 500) // Debounce to avoid too many requests
    }
  }

  const handleLLMOperatorChange = async (operator: string) => {
    setSettings(prev => ({
      ...prev,
      llmOperator: operator,
      selectedModel: '',
      availableModels: []
    }))

    // Set default URL based on operator
    let defaultUrl = ''
    switch (operator) {
      case 'Ollama':
        defaultUrl = 'http://localhost:11434'
        break
      case 'Groq':
        defaultUrl = 'https://api.groq.com/openai/v1'
        break
      case 'Claude':
        defaultUrl = 'https://api.anthropic.com/v1'
        break
      case 'Open Router':
        defaultUrl = 'https://openrouter.ai/api/v1'
        break
    }

    setSettings(prev => ({
      ...prev,
      llmUrl: defaultUrl
    }))

    // Load predefined models for non-Ollama providers
    if (operator !== 'Ollama') {
      const predefinedModels = getPredefinedModels(operator)
      setSettings(prev => ({
        ...prev,
        availableModels: predefinedModels
      }))
    }
  }

  const getPredefinedModels = (operator: string) => {
    switch (operator) {
      case 'Groq':
        return ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768']
      case 'Claude':
        return ['claude-3-haiku-20240307', 'claude-3-sonnet-20240229', 'claude-3-opus-20240229']
      case 'Open Router':
        return ['openai/gpt-4-turbo', 'anthropic/claude-3-sonnet', 'meta-llama/llama-3-8b-instruct']
      default:
        return []
    }
  }

  const testConnection = async () => {
    setTestingConnection(true)
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/test-connection/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          llm_operator: settings.llmOperator,
          llm_url: settings.llmUrl,
          api_key: settings.apiKey
        })
      })

      const data = await response.json()

      if (data.success) {
        if (data.available_models) {
          setSettings(prev => ({
            ...prev,
            availableModels: data.available_models
          }))
        }
        alert(data.message || 'Connection successful!')
      } else {
        alert(`Connection failed: ${data.error}`)
      }
    } catch (error) {
      alert(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setTestingConnection(false)
    }
  }

  const sections = [
    { id: 'general', name: 'General', icon: Settings },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Globe },
    { id: 'ai', name: 'AI Settings', icon: Brain },
    { id: 'data', name: 'Data Management', icon: Database },
    { id: 'workflows', name: 'Workflows', icon: Workflow },
    { id: 'scheduling', name: 'Scheduling', icon: Calendar }
  ]

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          System Name
        </label>
        <input
          type="text"
          value={settings.systemName}
          onChange={(e) => handleInputChange('systemName', e.target.value)}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
        />
      </div>
      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          System Description
        </label>
        <textarea
          value={settings.systemDescription}
          onChange={(e) => handleInputChange('systemDescription', e.target.value)}
          rows={3}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
        />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Timezone
          </label>
          <select
            value={settings.timezone}
            onChange={(e) => handleInputChange('timezone', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          >
            <option value="UTC">UTC</option>
            <option value="America/New_York">Eastern Time</option>
            <option value="America/Chicago">Central Time</option>
            <option value="America/Denver">Mountain Time</option>
            <option value="America/Los_Angeles">Pacific Time</option>
            <option value="Asia/Kolkata">India Standard Time</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Date Format
          </label>
          <select
            value={settings.dateFormat}
            onChange={(e) => handleInputChange('dateFormat', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          >
            <option value="YYYY-MM-DD">YYYY-MM-DD</option>
            <option value="MM/DD/YYYY">MM/DD/YYYY</option>
            <option value="DD/MM/YYYY">DD/MM/YYYY</option>
          </select>
        </div>
      </div>
    </div>
  )

  const fixOllamaConfiguration = async () => {
    setTestingConnection(true)
    try {
      const response = await fetch(`${API_BASE_URL}/api/settings/fix-ollama/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      const data = await response.json()

      if (data.success) {
        alert('Ollama configuration fixed successfully!')
        // Reload settings to reflect changes
        loadSettings()
      } else {
        alert(`Failed to fix Ollama configuration: ${data.error}`)
      }
    } catch (error) {
      console.error('Error fixing Ollama configuration:', error)
      alert('Network error occurred while fixing Ollama configuration')
    } finally {
      setTestingConnection(false)
    }
  }

  const getProviderStatus = (provider: string) => {
    // This would be enhanced to check actual provider configuration status
    if (provider === settings.llmOperator) {
      return settings.selectedModel ? '✓ Configured' : '⚠ Needs Configuration'
    }
    return ''
  }

  const renderAISettings = () => {
    try {
      return (
        <div className="space-y-6">
          <div className="bg-muted/50 rounded-lg p-4">
            <h4 className="font-medium text-card-foreground mb-2">AI Settings</h4>
            <p className="text-sm text-muted-foreground">Configure AI providers and select the active provider for AI-powered features.</p>
          </div>

          {/* AI Tabs */}
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveAITab('selection')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeAITab === 'selection'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-card-foreground hover:border-muted'
                }`}
              >
                Selected AI Operator
              </button>
              <button
                onClick={() => setActiveAITab('configuration')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeAITab === 'configuration'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-card-foreground hover:border-muted'
                }`}
              >
                AI Configuration
              </button>
            </nav>
          </div>

          {activeAITab === 'selection' ? renderAISelection() : renderAIConfiguration()}
        </div>
      )
    } catch (error) {
      console.error('Error in renderAISettings:', error)
      return (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">Error loading AI settings. Check console for details.</p>
        </div>
      )
    }
  }

  const renderAISelection = () => {
    try {
      return (
        <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Active AI Provider</h4>
        <p className="text-sm text-blue-800 mb-4">
          This provider will be used for all AI-powered features including bulk resume evaluation.
        </p>

        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="font-medium text-card-foreground">{settings.llmOperator}</span>
          </div>
          {settings.selectedModel && (
            <div className="text-sm text-muted-foreground">
              Model: {settings.selectedModel}
            </div>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          Select AI Provider
        </label>
        <select
          value={settings.llmOperator}
          onChange={(e) => handleLLMOperatorChange(e.target.value)}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
        >
          <option value="Ollama">Ollama {getProviderStatus('Ollama')}</option>
          <option value="Groq">Groq {getProviderStatus('Groq')}</option>
          <option value="Claude">Claude {getProviderStatus('Claude')}</option>
          <option value="Open Router">Open Router {getProviderStatus('Open Router')}</option>
        </select>
        <p className="text-xs text-muted-foreground mt-1">
          Change the active provider for all AI features. Make sure to configure the provider in the AI Configuration tab.
        </p>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Selection'}
        </button>
      </div>
        </div>
      )
    } catch (error) {
      console.error('Error in renderAISelection:', error)
      return <div className="p-4 text-red-600">Error loading AI selection</div>
    }
  }

  const renderAIConfiguration = () => {
    try {
      return (
        <div className="space-y-6">
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="font-medium text-card-foreground mb-2">Configure {settings.llmOperator}</h4>
        <p className="text-sm text-muted-foreground">Configure settings for the selected AI provider.</p>
      </div>

      {settings.llmOperator !== 'Ollama' && (
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            API Key
          </label>
          <input
            type="password"
            value={settings.apiKey}
            onChange={(e) => handleInputChange('apiKey', e.target.value)}
            placeholder="Enter your API key"
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          URL Endpoint
        </label>
        <div className="flex space-x-2">
          <input
            type="url"
            value={settings.llmUrl}
            onChange={(e) => handleInputChange('llmUrl', e.target.value)}
            placeholder="Enter the API endpoint URL"
            className="flex-1 px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
          <button
            onClick={testConnection}
            disabled={testingConnection || !settings.llmUrl}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary-foreground bg-primary rounded-md hover:bg-primary/90 disabled:opacity-50"
          >
            {testingConnection ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Database className="h-4 w-4 mr-2" />
            )}
            {testingConnection ? 'Testing...' : 'Test Connection'}
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          LLM Model
        </label>
        <select
          value={settings.selectedModel}
          onChange={(e) => handleInputChange('selectedModel', e.target.value)}
          disabled={settings.availableModels.length === 0}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground disabled:opacity-50"
        >
          <option value="">
            {settings.availableModels.length === 0
              ? (settings.llmOperator === 'Ollama' ? 'Test connection to load models' : 'No models available')
              : 'Select a model'
            }
          </option>
          {settings.availableModels.map((model) => (
            <option key={model} value={model}>
              {model}
            </option>
          ))}
        </select>
      </div>

      {settings.llmOperator === 'Ollama' && settings.availableModels.length === 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <p className="text-sm text-yellow-800">
            Click "Test Connection" to load available models from your Ollama instance.
          </p>
        </div>
      )}

      <div className="flex justify-end space-x-4">
        {settings.llmOperator === 'Ollama' && (
          <button
            onClick={fixOllamaConfiguration}
            disabled={testingConnection}
            className="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50"
          >
            {testingConnection ? 'Fixing...' : 'Fix Ollama Config'}
          </button>
        )}
        <button
          onClick={testConnection}
          disabled={testingConnection}
          className="inline-flex items-center px-4 py-2 border border-input rounded-md bg-background text-card-foreground hover:bg-muted disabled:opacity-50"
        >
          {testingConnection ? 'Testing...' : 'Test Connection'}
        </button>
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
        >
          {saving ? 'Saving...' : 'Save Configuration'}
        </button>
      </div>
        </div>
      )
    } catch (error) {
      console.error('Error in renderAIConfiguration:', error)
      return <div className="p-4 text-red-600">Error loading AI configuration</div>
    }
  }

  const renderDataManagementSettings = () => (
    <div className="space-y-6">
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="font-medium text-card-foreground mb-2">Data Management</h4>
        <p className="text-sm text-muted-foreground">Configure data retention, backup, and storage settings.</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Data Retention (Days)
          </label>
          <input
            type="number"
            value={settings.dataRetentionDays}
            onChange={(e) => handleInputChange('dataRetentionDays', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Backup Frequency
          </label>
          <select
            value={settings.backupFrequency}
            onChange={(e) => handleInputChange('backupFrequency', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="enableDataBackup"
          checked={settings.enableDataBackup}
          onChange={(e) => handleInputChange('enableDataBackup', e.target.checked)}
          className="rounded border-input"
        />
        <label htmlFor="enableDataBackup" className="text-sm text-card-foreground">
          Enable automatic data backup
        </label>
      </div>
    </div>
  )

  const renderWorkflowSettings = () => (
    <div className="space-y-6">
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="font-medium text-card-foreground mb-2">Workflow Automation</h4>
        <p className="text-sm text-muted-foreground">Configure automated workflows and processes.</p>
      </div>

      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="enableWorkflowAutomation"
          checked={settings.enableWorkflowAutomation}
          onChange={(e) => handleInputChange('enableWorkflowAutomation', e.target.checked)}
          className="rounded border-input"
        />
        <label htmlFor="enableWorkflowAutomation" className="text-sm text-card-foreground">
          Enable workflow automation
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          Default Workflow Timeout (Hours)
        </label>
        <input
          type="number"
          value={settings.defaultWorkflowTimeout}
          onChange={(e) => handleInputChange('defaultWorkflowTimeout', e.target.value)}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
        />
      </div>
    </div>
  )

  const renderSchedulingSettings = () => (
    <div className="space-y-6">
      <div className="bg-muted/50 rounded-lg p-4">
        <h4 className="font-medium text-card-foreground mb-2">Scheduling Configuration</h4>
        <p className="text-sm text-muted-foreground">Configure default scheduling and working hours settings.</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-card-foreground mb-2">
          Default Meeting Duration (Minutes)
        </label>
        <select
          value={settings.defaultMeetingDuration}
          onChange={(e) => handleInputChange('defaultMeetingDuration', e.target.value)}
          className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
        >
          <option value="30">30 minutes</option>
          <option value="45">45 minutes</option>
          <option value="60">60 minutes</option>
          <option value="90">90 minutes</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Working Hours Start
          </label>
          <input
            type="time"
            value={settings.workingHoursStart}
            onChange={(e) => handleInputChange('workingHoursStart', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Working Hours End
          </label>
          <input
            type="time"
            value={settings.workingHoursEnd}
            onChange={(e) => handleInputChange('workingHoursEnd', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
      </div>
    </div>
  )



  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Session Timeout (minutes)
          </label>
          <input
            type="number"
            value={settings.sessionTimeout}
            onChange={(e) => handleInputChange('sessionTimeout', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-card-foreground mb-2">
            Minimum Password Length
          </label>
          <input
            type="number"
            value={settings.passwordMinLength}
            onChange={(e) => handleInputChange('passwordMinLength', e.target.value)}
            className="w-full px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
          />
        </div>
      </div>
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="requireSpecialChars"
            checked={settings.requireSpecialChars}
            onChange={(e) => handleInputChange('requireSpecialChars', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="requireSpecialChars" className="text-sm text-card-foreground">
            Require special characters in passwords
          </label>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="enableTwoFactor"
            checked={settings.enableTwoFactor}
            onChange={(e) => handleInputChange('enableTwoFactor', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="enableTwoFactor" className="text-sm text-card-foreground">
            Enable two-factor authentication
          </label>
        </div>
      </div>
    </div>
  )



  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="enableEmailNotifications"
            checked={settings.enableEmailNotifications}
            onChange={(e) => handleInputChange('enableEmailNotifications', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="enableEmailNotifications" className="text-sm text-card-foreground">
            Enable email notifications
          </label>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="enableJobAlerts"
            checked={settings.enableJobAlerts}
            onChange={(e) => handleInputChange('enableJobAlerts', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="enableJobAlerts" className="text-sm text-card-foreground">
            Enable job posting alerts
          </label>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="enableCandidateAlerts"
            checked={settings.enableCandidateAlerts}
            onChange={(e) => handleInputChange('enableCandidateAlerts', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="enableCandidateAlerts" className="text-sm text-card-foreground">
            Enable candidate application alerts
          </label>
        </div>
        <div className="flex items-center space-x-3">
          <input
            type="checkbox"
            id="enableInterviewReminders"
            checked={settings.enableInterviewReminders}
            onChange={(e) => handleInputChange('enableInterviewReminders', e.target.checked)}
            className="rounded border-input"
          />
          <label htmlFor="enableInterviewReminders" className="text-sm text-card-foreground">
            Enable interview reminders
          </label>
        </div>
      </div>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'general': return renderGeneralSettings()
      case 'security': return renderSecuritySettings()
      case 'notifications': return renderNotificationSettings()
      case 'ai': return renderAISettings()
      case 'data': return renderDataManagementSettings()
      case 'workflows': return renderWorkflowSettings()
      case 'scheduling': return renderSchedulingSettings()
      default: return renderGeneralSettings()
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-muted-foreground">Loading settings...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground hover:text-card-foreground rounded-md hover:bg-muted"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Admin
          </button>
          <div>
            <h2 className="text-2xl font-bold text-card-foreground flex items-center">
              <Settings className="h-6 w-6 mr-2" />
              System Settings
            </h2>
            <p className="text-muted-foreground">Configure system preferences and settings</p>
          </div>
        </div>
        <button
          onClick={handleSave}
          disabled={saving}
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary-foreground bg-primary rounded-md hover:bg-primary/90 disabled:opacity-50"
        >
          {saving ? (
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          ) : (
            <Save className="h-4 w-4 mr-2" />
          )}
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-card rounded-lg border border-border p-4">
            <nav className="space-y-2">
              {sections.map((section) => {
                const Icon = section.icon
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors ${
                      activeSection === section.id
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-card-foreground hover:bg-muted'
                    }`}
                  >
                    <Icon className="h-4 w-4 mr-3" />
                    {section.name}
                  </button>
                )
              })}
            </nav>
          </div>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-card rounded-lg border border-border p-6">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  )
}
