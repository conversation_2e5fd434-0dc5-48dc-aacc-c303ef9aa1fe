import { useState, useEffect } from 'react'
import { X, Briefcase, Calendar, User, Building, AlertCircle } from 'lucide-react'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface EditJobModalProps {
  isOpen: boolean
  onClose: () => void
  onJobUpdated: () => void
  jobId: string
}

export default function EditJobModal({ isOpen, onClose, onJobUpdated, jobId }: EditJobModalProps) {
  // Form state
  const [ticketId, setTicketId] = useState('')
  const [jobTitle, setJobTitle] = useState('')
  const [jobRoleId, setJobRoleId] = useState('')
  const [date, setDate] = useState('')
  const [recruiter, setRecruiter] = useState('')
  const [taIncharge, setTaIncharge] = useState('')
  const [client, setClient] = useState('')
  const [interviewPanel, setInterviewPanel] = useState('')
  const [sourcingType, setSourcingType] = useState('')
  const [priority, setPriority] = useState('')
  const [candidatesApplied, setCandidatesApplied] = useState('')
  const [candidatesInterviewed, setCandidatesInterviewed] = useState('')
  const [availability, setAvailability] = useState('')

  // Other state
  const [jobRoles, setJobRoles] = useState<JobRole[]>([])
  const [loading, setLoading] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (isOpen && jobId) {
      fetchJobRoles()
      fetchJobDetails()
    }
  }, [isOpen, jobId])

  const fetchJobRoles = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/job-management/job-roles/', {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data && Array.isArray(data.data.job_roles)) {
          setJobRoles(data.data.job_roles)
        }
      }
    } catch (error) {
      console.error('Error fetching job roles:', error)
    }
  }

  const fetchJobDetails = async () => {
    setLoading(true)
    try {
      const response = await fetch(`http://localhost:8000/api/job-management/jobs/${jobId}/`, {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          const job = data.data
          // Populate form with job data
          setTicketId(job.ticket_id || '')
          setJobTitle(job.job_title || '')
          setJobRoleId(job.job_role_id || '')
          setDate(job.date || '')
          setRecruiter(job.recruiter || '')
          setTaIncharge(job.ta_incharge || '')
          setClient(job.client || '')
          setInterviewPanel(job.interview_panel || '')
          setSourcingType(job.sourcing_type || '')
          setPriority(job.priority || '')
          setCandidatesApplied(job.candidates_applied?.toString() || '')
          setCandidatesInterviewed(job.candidates_interviewed?.toString() || '')
          setAvailability(job.availability || '')
        }
      } else {
        setError('Failed to load job details')
      }
    } catch (error) {
      console.error('Error fetching job details:', error)
      setError('Failed to load job details')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setUpdating(true)
    setError('')

    try {
      const jobData = {
        ticket_id: ticketId.trim(),
        job_title: jobTitle.trim(),
        job_role_id: jobRoleId,
        date: date,
        recruiter: recruiter.trim(),
        ta_incharge: taIncharge.trim(),
        client: client.trim(),
        interview_panel: interviewPanel.trim(),
        sourcing_type: sourcingType,
        priority: priority,
        candidates_applied: candidatesApplied ? parseInt(candidatesApplied) : 0,
        candidates_interviewed: candidatesInterviewed ? parseInt(candidatesInterviewed) : 0,
        availability: availability
      }

      const response = await fetch(`http://localhost:8000/api/job-management/jobs/${jobId}/update/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(jobData)
      })

      const result = await response.json()

      if (result.success) {
        onJobUpdated()
        onClose()
      } else {
        setError(result.error || 'Failed to update job')
      }
    } catch (error) {
      console.error('Error updating job:', error)
      setError('Network error')
    } finally {
      setUpdating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-card-foreground flex items-center">
            <Briefcase className="h-5 w-5 mr-2" />
            Edit Job
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-md transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="mb-4 p-4 bg-destructive/15 text-destructive rounded-md flex items-center">
              <AlertCircle className="h-4 w-4 mr-2" />
              {error}
            </div>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading job details...</div>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="ticketId" className="block text-sm font-medium mb-1">
                    Ticket ID *
                  </label>
                  <input
                    id="ticketId"
                    type="text"
                    value={ticketId}
                    onChange={(e) => setTicketId(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter ticket ID"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="jobTitle" className="block text-sm font-medium mb-1">
                    Job Title *
                  </label>
                  <input
                    id="jobTitle"
                    type="text"
                    value={jobTitle}
                    onChange={(e) => setJobTitle(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter job title"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="jobRole" className="block text-sm font-medium mb-1">
                    Job Role *
                  </label>
                  <select
                    id="jobRole"
                    value={jobRoleId}
                    onChange={(e) => setJobRoleId(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  >
                    <option value="">Select Job Role</option>
                    {Array.isArray(jobRoles) && jobRoles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.role_name} ({role.years_of_experience} years exp.)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="date" className="block text-sm font-medium mb-1">
                    Date *
                  </label>
                  <input
                    id="date"
                    type="date"
                    value={date}
                    onChange={(e) => setDate(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="recruiter" className="block text-sm font-medium mb-1">
                    Recruiter *
                  </label>
                  <input
                    id="recruiter"
                    type="text"
                    value={recruiter}
                    onChange={(e) => setRecruiter(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter recruiter name"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="taIncharge" className="block text-sm font-medium mb-1">
                    TA Incharge *
                  </label>
                  <input
                    id="taIncharge"
                    type="text"
                    value={taIncharge}
                    onChange={(e) => setTaIncharge(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter TA incharge name"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="client" className="block text-sm font-medium mb-1">
                    Client *
                  </label>
                  <input
                    id="client"
                    type="text"
                    value={client}
                    onChange={(e) => setClient(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter client name"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="interviewPanel" className="block text-sm font-medium mb-1">
                    Interview Panel
                  </label>
                  <input
                    id="interviewPanel"
                    type="text"
                    value={interviewPanel}
                    onChange={(e) => setInterviewPanel(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter interview panel"
                  />
                </div>

                <div>
                  <label htmlFor="sourcingType" className="block text-sm font-medium mb-1">
                    Sourcing Type *
                  </label>
                  <select
                    id="sourcingType"
                    value={sourcingType}
                    onChange={(e) => setSourcingType(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  >
                    <option value="">Select sourcing type</option>
                    <option value="Internal">Internal</option>
                    <option value="External">External</option>
                    <option value="Mixed">Mixed</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="priority" className="block text-sm font-medium mb-1">
                    Priority *
                  </label>
                  <select
                    id="priority"
                    value={priority}
                    onChange={(e) => setPriority(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  >
                    <option value="">Select priority</option>
                    <option value="High">High</option>
                    <option value="Medium">Medium</option>
                    <option value="Low">Low</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="candidatesApplied" className="block text-sm font-medium mb-1">
                    Candidates Applied
                  </label>
                  <input
                    id="candidatesApplied"
                    type="number"
                    min="0"
                    value={candidatesApplied}
                    onChange={(e) => setCandidatesApplied(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label htmlFor="candidatesInterviewed" className="block text-sm font-medium mb-1">
                    Candidates Interviewed
                  </label>
                  <input
                    id="candidatesInterviewed"
                    type="number"
                    min="0"
                    value={candidatesInterviewed}
                    onChange={(e) => setCandidatesInterviewed(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="0"
                  />
                </div>

                <div>
                  <label htmlFor="availability" className="block text-sm font-medium mb-1">
                    Availability *
                  </label>
                  <select
                    id="availability"
                    value={availability}
                    onChange={(e) => setAvailability(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    required
                  >
                    <option value="">Select availability</option>
                    <option value="Open">Open</option>
                    <option value="Closed">Closed</option>
                    <option value="On Hold">On Hold</option>
                  </select>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex items-center justify-end space-x-4 pt-4 border-t border-border">
                <button
                  type="button"
                  onClick={onClose}
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={updating}
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  {updating ? 'Updating...' : 'Update Job'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  )
}
