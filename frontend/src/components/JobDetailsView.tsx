import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Briefcase, Calendar, User, Users, Building, AlertTriangle, Plus, Edit, Trash2, MoreVertical, FileText, Upload, Save, ChevronDown, ChevronRight } from 'lucide-react'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface Job {
  id: string
  ticket_id: string
  job_title: string
  job_role_id: string
  job_role?: JobRole
  date: string
  recruiter: string
  ta_incharge: string
  client: string
  interview_panel: string
  sourcing_type: string
  priority: string
  candidates_applied: number
  candidates_interviewed: number
  availability: string
  vendor_id: string
  default_job_description?: string
  additional_job_description?: string
  job_description_filename?: string
  is_active: boolean
  is_published: boolean
  created_at: string
}

interface JobDetailsViewProps {
  jobId: string
  onBack: () => void
  onBulkEvaluation?: (jobId: string) => void
  onEditJob?: (jobId: string) => void
}

export default function JobDetailsView({ jobId, onBack, onBulkEvaluation, onEditJob }: JobDetailsViewProps) {
  const navigate = useNavigate()
  const [job, setJob] = useState<Job | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showActionsDropdown, setShowActionsDropdown] = useState(false)
  const [additionalDescription, setAdditionalDescription] = useState('')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [saving, setSaving] = useState(false)
  const [isJobDescriptionExpanded, setIsJobDescriptionExpanded] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (jobId) {
      fetchJobDetails(jobId)
    }
  }, [jobId])

  useEffect(() => {
    if (job) {
      setAdditionalDescription(job.additional_job_description || '')
    }
  }, [job])

  // Handle clicking outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowActionsDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const fetchJobDetails = async (id: string) => {
    try {
      const response = await fetch(`http://localhost:8000/api/job-management/jobs/${id}/`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJob(data.data)
        } else {
          setError('Failed to load job details')
        }
      } else {
        setError('Failed to load job details')
      }
    } catch (error) {
      console.error('Error fetching job details:', error)
      setError('Failed to load job details')
    } finally {
      setLoading(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low': return 'text-green-600 bg-green-50 border-green-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getSourcingTypeColor = (type: string) => {
    switch (type) {
      case 'direct': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'vendor': return 'text-purple-600 bg-purple-50 border-purple-200'
      case 'internal': return 'text-green-600 bg-green-50 border-green-200'
      case 'all': return 'text-orange-600 bg-orange-50 border-orange-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getAvailabilityColor = (availability: string) => {
    switch (availability) {
      case 'vacant': return 'text-green-600 bg-green-50 border-green-200'
      case 'hired': return 'text-gray-600 bg-gray-50 border-gray-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const handleSaveDescription = async () => {
    setSaving(true)
    try {
      const formData = new FormData()
      formData.append('additional_job_description', additionalDescription)

      if (selectedFile) {
        formData.append('job_description_file', selectedFile)
      }

      const response = await fetch(`http://localhost:8000/api/job-management/jobs/${job?.id}/description/`, {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      const data = await response.json()

      if (data.success) {
        alert('Job description saved successfully!')
        setSelectedFile(null)
        // Refresh job data
        if (jobId) {
          fetchJobDetails(jobId)
        }
      } else {
        alert(`Failed to save: ${data.error}`)
      }
    } catch (error) {
      alert('Network error occurred')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading job details...</div>
      </div>
    )
  }

  if (error || !job) {
    return (
      <div className="text-center py-12">
        <div className="text-destructive mb-4">{error || 'Job not found'}</div>
        <button
          onClick={onBack}
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary-foreground bg-primary rounded-md hover:bg-primary/90"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBack}
            className="inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground hover:text-card-foreground rounded-md hover:bg-muted"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Jobs
          </button>
          <div>
            <div className="flex items-center space-x-2">
              <Briefcase className="h-6 w-6 text-primary" />
              <h1 className="text-2xl font-bold text-card-foreground">
                {job.job_title}
              </h1>
            </div>
            <p className="text-muted-foreground mt-1">
              Ticket: {job.ticket_id} • {job.job_role?.role_name} ({job.job_role?.years_of_experience} years exp.)
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowActionsDropdown(!showActionsDropdown)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-card-foreground bg-background border border-input rounded-md hover:bg-muted"
              title="Actions"
            >
              <MoreVertical className="h-4 w-4 mr-2" />
              Actions
            </button>

            {showActionsDropdown && (
              <div className="absolute right-0 mt-2 w-48 bg-background border border-border rounded-md shadow-lg z-10">
                <div className="py-1">
                  <button
                    onClick={() => {
                      if (onBulkEvaluation && job) {
                        onBulkEvaluation(job.id)
                      }
                      setShowActionsDropdown(false)
                    }}
                    className="w-full text-left px-4 py-2 text-sm hover:bg-muted transition-colors"
                  >
                    Evaluate Resumes in Bulk
                  </button>
                </div>
              </div>
            )}
          </div>
          <button
            onClick={() => onEditJob?.(jobId)}
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-card-foreground bg-background border border-input rounded-md hover:bg-muted"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Job
          </button>
          <button className="inline-flex items-center px-4 py-2 text-sm font-medium text-destructive bg-background border border-input rounded-md hover:bg-muted">
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {/* Statistics */}
        <div className="bg-card rounded-lg border border-border p-6">
          <h2 className="text-lg font-semibold text-card-foreground mb-4">Statistics</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">{job.candidates_applied}</div>
              <div className="text-sm text-muted-foreground">Candidates Applied</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-primary">{job.candidates_interviewed}</div>
              <div className="text-sm text-muted-foreground">Candidates Interviewed</div>
            </div>
            <div className="text-center">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getSourcingTypeColor(job.sourcing_type || 'direct')}`}>
                {job.sourcing_type ? job.sourcing_type.charAt(0).toUpperCase() + job.sourcing_type.slice(1) : 'Not specified'}
              </span>
              <div className="text-sm text-muted-foreground mt-1">Sourcing Type</div>
            </div>
            <div className="text-center">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getAvailabilityColor(job.availability || 'vacant')}`}>
                {job.availability ? job.availability.charAt(0).toUpperCase() + job.availability.slice(1) : 'Not specified'}
              </span>
              <div className="text-sm text-muted-foreground mt-1">Availability</div>
            </div>
          </div>
        </div>

        {/* Job Information */}
        <div className="bg-card rounded-lg border border-border p-6">
          <h2 className="text-lg font-semibold text-card-foreground mb-4">Job Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center space-x-3">
                <Calendar className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Date</div>
                  <div className="font-medium text-card-foreground">{job.date}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Recruiter</div>
                  <div className="font-medium text-card-foreground">{job.recruiter}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <User className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">TA Incharge</div>
                  <div className="font-medium text-card-foreground">{job.ta_incharge}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Building className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Client</div>
                  <div className="font-medium text-card-foreground">{job.client}</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Users className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Interview Panel</div>
                  <div className="font-medium text-card-foreground">
                    {job.interview_panel || 'Not specified'}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-muted-foreground" />
                <div>
                  <div className="text-sm text-muted-foreground">Priority</div>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getPriorityColor(job.priority || 'medium')}`}>
                    {job.priority ? job.priority.charAt(0).toUpperCase() + job.priority.slice(1) : 'Not specified'}
                  </span>
                </div>
              </div>
            </div>
          </div>

        {/* Job Description Section */}
        <div className="bg-card rounded-lg border border-border">
          <button
            onClick={() => setIsJobDescriptionExpanded(!isJobDescriptionExpanded)}
            className="w-full flex items-center justify-between p-6 text-left hover:bg-muted/50 transition-colors"
          >
            <div className="flex items-center">
              <FileText className="h-5 w-5 text-primary mr-2" />
              <h3 className="text-lg font-semibold text-card-foreground">Job Description</h3>
            </div>
            {isJobDescriptionExpanded ? (
              <ChevronDown className="h-5 w-5 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-5 w-5 text-muted-foreground" />
            )}
          </button>

          {isJobDescriptionExpanded && (
            <div className="px-6 pb-6 space-y-6 border-t border-border">
            {/* Default Description */}
            <div>
              <label className="block text-sm font-medium text-card-foreground mb-2">
                Default
              </label>
              <div className="p-3 bg-muted/30 rounded-md border min-h-[100px]">
                {job?.default_job_description ? (
                  <p className="text-sm text-card-foreground whitespace-pre-wrap">
                    {job.default_job_description}
                  </p>
                ) : (
                  <p className="text-sm text-muted-foreground italic">
                    No default job description available. This would be populated from the job role.
                  </p>
                )}
              </div>
            </div>

            {/* Additional Description */}
            <div>
              <label className="block text-sm font-medium text-card-foreground mb-2">
                Additional
              </label>
              <textarea
                value={additionalDescription}
                onChange={(e) => setAdditionalDescription(e.target.value)}
                placeholder="Add additional job description details..."
                className="w-full p-3 border border-input rounded-md bg-background text-card-foreground min-h-[100px] resize-vertical"
              />
            </div>

            {/* File Upload */}
            <div>
              <label className="block text-sm font-medium text-card-foreground mb-2">
                Upload Job Description File
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                  className="hidden"
                  id="job-description-file"
                />
                <label
                  htmlFor="job-description-file"
                  className="inline-flex items-center px-4 py-2 border border-input rounded-md bg-background text-card-foreground hover:bg-muted cursor-pointer"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose File
                </label>
                {selectedFile && (
                  <span className="text-sm text-muted-foreground">
                    {selectedFile.name}
                  </span>
                )}
                {job?.job_description_filename && !selectedFile && (
                  <span className="text-sm text-muted-foreground">
                    Current: {job.job_description_filename}
                  </span>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Supported formats: PDF, DOC, DOCX, TXT
              </p>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button
                onClick={handleSaveDescription}
                disabled={saving}
                className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : 'Save Description'}
              </button>
            </div>
            </div>
          )}
        </div>

        {/* Candidates Section */}
        <div className="bg-card rounded-lg border border-border p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-card-foreground">Candidates</h3>
            <button className="inline-flex items-center px-3 py-2 text-sm font-medium text-primary-foreground bg-primary rounded-md hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Add Candidate
            </button>
          </div>

          <div className="text-center py-12">
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h4 className="text-lg font-medium text-card-foreground mb-2">No candidates added yet</h4>
            <p className="text-muted-foreground mb-4">
              Start building your candidate pipeline by adding candidates to this job.
            </p>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary-foreground bg-primary rounded-md hover:bg-primary/90">
              <Plus className="h-4 w-4 mr-2" />
              Add First Candidate
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
