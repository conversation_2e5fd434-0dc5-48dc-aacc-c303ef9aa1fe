import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render, mockFetch, mockUser, mockApiResponse } from '../../test/utils'
import UserManagement from '../UserManagement'

const mockOnBack = vi.fn()

describe('UserManagement Component', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = mockFetch({
      success: true,
      data: {
        users: [mockUser],
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_count: 1,
          has_next: false,
          has_previous: false,
        }
      }
    })
  })

  it('renders user management interface', () => {
    render(<UserManagement onBack={mockOnBack} />)

    expect(screen.getByText('User Management')).toBeInTheDocument()
    expect(screen.getByText('Manage system users and their permissions')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /add user/i })).toBeInTheDocument()
  })

  it('has search functionality', () => {
    render(<UserManagement onBack={mockOnBack} />)

    expect(screen.getByPlaceholderText(/search users/i)).toBeInTheDocument()
  })

  it('has search button', () => {
    render(<UserManagement onBack={mockOnBack} />)

    const buttons = screen.getAllByRole('button')
    const hasSearchButton = buttons.some(btn =>
      btn.textContent?.toLowerCase().includes('search') ||
      btn.getAttribute('title')?.toLowerCase().includes('search')
    )
    expect(hasSearchButton).toBe(true)
  })

  it('opens create user modal when add user button is clicked', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)

    const addButton = screen.getByRole('button', { name: /add user/i })
    await user.click(addButton)

    expect(screen.getByText('Create New User')).toBeInTheDocument()
  })

  it('has table structure', () => {
    render(<UserManagement onBack={mockOnBack} />)

    // Should have table headers
    expect(screen.getByText('Email')).toBeInTheDocument()
    expect(screen.getByText('Name')).toBeInTheDocument()
    expect(screen.getByText('Type')).toBeInTheDocument()
    expect(screen.getByText('Status')).toBeInTheDocument()
  })

  it('has back button functionality', async () => {
    const user = userEvent.setup()
    render(<UserManagement onBack={mockOnBack} />)

    const backButton = screen.getByRole('button', { name: /back/i })
    await user.click(backButton)

    expect(mockOnBack).toHaveBeenCalled()
  })

})
