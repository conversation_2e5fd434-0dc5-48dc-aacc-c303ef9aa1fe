import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Calendar, Plus, Search, ArrowLeft, Clock, Users, Video, MoreVertical, ChevronDown } from 'lucide-react'
import ScheduleInterviewModal from './ScheduleInterviewModal'

interface Interview {
  id: string
  title: string
  scheduled_date: string
  scheduled_time: string
  candidate_id: string
  status: string
  interview_type: string
  interview_mode: string
}

interface PaginationData {
  current_page: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

interface InterviewManagementProps {
  onBack?: () => void
}

export default function InterviewManagement({ onBack }: InterviewManagementProps) {
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [showScheduleModal, setShowScheduleModal] = useState(false)
  const [selectedInterviews, setSelectedInterviews] = useState<Set<string>>(new Set())
  const [selectAll, setSelectAll] = useState(false)
  const [showActionsDropdown, setShowActionsDropdown] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    fetchInterviews()
  }, [currentPage, searchTerm])

  const fetchInterviews = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '20'
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`http://localhost:8000/api/interviews/interviews/?${params}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setInterviews(data.data)
          setPagination(data.pagination)
        } else {
          setError(data.error || 'Failed to fetch interviews')
        }
      } else {
        setError('Failed to fetch interviews')
      }
    } catch (error) {
      console.error('Error fetching interviews:', error)
      setError('Failed to fetch interviews')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchInterviews()
  }

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
    // Scroll to bottom to keep pagination visible
    setTimeout(() => {
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
    }, 100)
  }

  const handleSelectInterview = (interviewId: string) => {
    const newSelected = new Set(selectedInterviews)
    if (newSelected.has(interviewId)) {
      newSelected.delete(interviewId)
    } else {
      newSelected.add(interviewId)
    }
    setSelectedInterviews(newSelected)
    setSelectAll(newSelected.size === interviews.length && interviews.length > 0)
  }

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedInterviews(new Set())
      setSelectAll(false)
    } else {
      setSelectedInterviews(new Set(interviews.map(interview => interview.id)))
      setSelectAll(true)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {onBack && (
              <button
                onClick={onBack}
                className="mr-4 p-2 rounded-md hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
            <div>
              <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                <Calendar className="h-6 w-6 mr-2" />
                Interviews
              </h2>
              <p className="text-muted-foreground">Manage interview schedules and feedback</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Actions Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowActionsDropdown(!showActionsDropdown)}
                disabled={selectedInterviews.size === 0}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
              >
                <MoreVertical className="h-4 w-4 mr-2" />
                Actions ({selectedInterviews.size})
              </button>

              {showActionsDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-background border border-border rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <button
                      onClick={() => {
                        alert(`Delete ${selectedInterviews.size} selected interviews`)
                        setShowActionsDropdown(false)
                      }}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-muted transition-colors text-destructive"
                    >
                      Delete Selected
                    </button>
                    <button
                      onClick={() => {
                        alert(`Export ${selectedInterviews.size} selected interviews`)
                        setShowActionsDropdown(false)
                      }}
                      className="w-full text-left px-4 py-2 text-sm hover:bg-muted transition-colors"
                    >
                      Export Selected
                    </button>
                  </div>
                </div>
              )}
            </div>

            <button
              onClick={() => setShowScheduleModal(true)}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Schedule Interview
            </button>
          </div>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search interviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-input bg-background pl-8 pr-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
          >
            Search
          </button>
        </form>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Interviews Table */}
      <div className="bg-card rounded-lg border border-border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="rounded border-input"
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Interview</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Date & Time</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Type</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Mode</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center text-muted-foreground">
                    Loading interviews...
                  </td>
                </tr>
              ) : interviews.length === 0 ? (
                <tr>
                  <td colSpan={7} className="px-4 py-8 text-center">
                    <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-card-foreground mb-2">No interviews found</h3>
                    <p className="text-muted-foreground mb-4">
                      {searchTerm ? 'No interviews match your search criteria.' : 'Get started by scheduling your first interview.'}
                    </p>
                    <button
                      onClick={() => setShowScheduleModal(true)}
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Schedule Interview
                    </button>
                  </td>
                </tr>
              ) : (
                interviews.map((interview) => (
                  <tr key={interview.id} className="border-b border-border hover:bg-muted/50">
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        checked={selectedInterviews.has(interview.id)}
                        onChange={() => handleSelectInterview(interview.id)}
                        className="rounded border-input"
                      />
                    </td>
                    <td className="px-4 py-4">
                      <span className="font-medium text-card-foreground">{interview.title}</span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center text-sm text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        {interview.scheduled_date} {interview.scheduled_time}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{interview.interview_type}</span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center text-sm text-muted-foreground">
                        {interview.interview_mode === 'Video Call' && <Video className="h-3 w-3 mr-1" />}
                        {interview.interview_mode === 'In-Person' && <Users className="h-3 w-3 mr-1" />}
                        {interview.interview_mode}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        interview.status === 'Scheduled' ? 'bg-blue-100 text-blue-800' :
                        interview.status === 'Completed' ? 'bg-green-100 text-green-800' :
                        interview.status === 'Cancelled' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {interview.status}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            // TODO: Implement view/edit interview functionality
                            alert('View interview functionality will be implemented later')
                          }}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="View interview"
                        >
                          <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination && pagination.total_pages > 1 && (
          <div className="flex items-center justify-between px-4 py-3 border-t border-border">
            <div className="text-sm text-muted-foreground">
              Showing {((pagination.current_page - 1) * 20) + 1} to {Math.min(pagination.current_page * 20, pagination.total_count)} of {pagination.total_count} interviews
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(Math.max(currentPage - 1, 1))}
                disabled={!pagination.has_previous}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-8 px-3"
              >
                Previous
              </button>
              <span className="text-sm text-muted-foreground">
                Page {pagination.current_page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!pagination.has_next}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-8 px-3"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Schedule Interview Modal */}
      <ScheduleInterviewModal
        isOpen={showScheduleModal}
        onClose={() => setShowScheduleModal(false)}
        onInterviewScheduled={fetchInterviews}
      />
    </div>
  )
}
