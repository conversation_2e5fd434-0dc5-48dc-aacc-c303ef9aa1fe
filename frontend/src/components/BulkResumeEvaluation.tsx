import { useState, useEffect } from 'react'
import { ArrowLeft, Upload, FileText, Plus, X, Play, BarChart3, CheckCircle, Download, Trash2, MoreVertical, Mail } from 'lucide-react'

interface Job {
  id: string
  job_title: string
  default_job_description?: string
  additional_job_description?: string
}

interface Resume {
  id: string
  file: File | string | null  // File object, 'database' for stored files, or null
  name: string
  size: string
  status: 'uploaded' | 'evaluating' | 'completed' | 'error'
}

interface EvaluationCriteria {
  id: string
  name: string
  isCustom: boolean
}

interface EvaluationResult {
  resumeId: string
  resumeName: string
  scores: { [criteriaId: string]: number }
  comments: { [criteriaId: string]: string }
  averageScore: number
  rank: number
}

const defaultCriteria: EvaluationCriteria[] = [
  { id: 'skills_match', name: 'Skills Match', isCustom: false },
  { id: 'experience_relevance', name: 'Experience Relevance', isCustom: false },
  { id: 'education_fit', name: 'Education Fit', isCustom: false },
  { id: 'technical_proficiency', name: 'Technical Proficiency', isCustom: false },
  { id: 'communication_skills', name: 'Communication Skills', isCustom: false },
  { id: 'leadership_experience', name: 'Leadership Experience', isCustom: false },
  { id: 'industry_knowledge', name: 'Industry Knowledge', isCustom: false },
  { id: 'cultural_fit', name: 'Cultural Fit', isCustom: false },
  { id: 'career_progression', name: 'Career Progression', isCustom: false },
  { id: 'certifications_training', name: 'Certifications & Training', isCustom: false }
]

interface BulkResumeEvaluationProps {
  jobId: string
  onBack: () => void
}

interface EvaluationSession {
  id: string
  job_id: string
  user_id: string
  session_name: string
  selected_criteria: string[]
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  total_resumes: number
  processed_resumes: number
  created_at: string
  updated_at: string
  completed_at?: string
  error_message?: string
}

interface UploadedResumeDB {
  id: string
  session_id: string
  filename: string
  original_filename: string
  file_size: number
  upload_order: number
  status: 'uploaded' | 'processing' | 'completed' | 'failed'
  uploaded_at: string
  processed_at?: string
  error_message?: string
}

export default function BulkResumeEvaluation({ jobId, onBack }: BulkResumeEvaluationProps) {
  
  const [job, setJob] = useState<Job | null>(null)
  const [session, setSession] = useState<EvaluationSession | null>(null)
  const [resumes, setResumes] = useState<Resume[]>([])
  const [uploadedResumes, setUploadedResumes] = useState<UploadedResumeDB[]>([])
  const [criteria, setCriteria] = useState<EvaluationCriteria[]>(defaultCriteria)
  const [selectedCriteria, setSelectedCriteria] = useState<Set<string>>(new Set())
  const [customCriteriaName, setCustomCriteriaName] = useState('')
  const [activeTab, setActiveTab] = useState<'upload' | 'criteria' | 'results'>('upload')
  const [evaluating, setEvaluating] = useState(false)
  const [results, setResults] = useState<EvaluationResult[]>([])
  const [loading, setLoading] = useState(true)
  const [showActionsDropdown, setShowActionsDropdown] = useState(false)

  // Progress tracking state
  const [evaluationProgress, setEvaluationProgress] = useState({
    current: 0,
    total: 0,
    startTime: 0,
    estimatedTimeRemaining: 0,
    averageTimePerResume: 0
  })

  // Track actual start time separately to avoid resetting
  const [evaluationStartTime, setEvaluationStartTime] = useState<number>(0)

  // Polling for evaluation status
  const [statusPolling, setStatusPolling] = useState<NodeJS.Timeout | null>(null)

  useEffect(() => {
    if (jobId) {
      console.log('useEffect triggered for jobId:', jobId)
      const initializeComponent = async () => {
        try {
          await fetchJobDetails()
          await loadOrCreateSession()
        } catch (error) {
          console.error('Error initializing component:', error)
        } finally {
          setLoading(false)
        }
      }

      // Add timeout to prevent infinite loading
      const timeout = setTimeout(() => {
        console.warn('Component initialization timed out')
        setLoading(false)
      }, 10000) // 10 second timeout
      initializeComponent().finally(() => {
        clearTimeout(timeout)
      })
    }
  }, [jobId])

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (statusPolling) {
        clearInterval(statusPolling)
      }
    }
  }, [statusPolling])

  const loadOrCreateSession = async () => {
    try {
      // Check if there's an existing session for this job
      const sessionId = localStorage.getItem(`evaluation-session-${jobId}`)
      console.log('Loading session for job:', jobId, 'Found sessionId:', sessionId)

      if (sessionId) {
        // Load existing session
        await loadExistingSession(sessionId)
      } else {
        // No existing session, start fresh
        console.log('No existing session found, starting fresh')
      }
    } catch (error) {
      console.error('Error loading session:', error)
      throw error // Re-throw to be caught by the useEffect
    }
  }

  const loadExistingSession = async (sessionId: string) => {
    try {
      console.log('Loading existing session:', sessionId)
      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${sessionId}/`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Session data received:', data)
        if (data.success) {
          setSession(data.session)
          setUploadedResumes(data.resumes || [])
          setSelectedCriteria(new Set(data.session.selected_criteria || []))
          setResults(data.results || [])

          // Convert uploaded resumes to UI format
          const uiResumes: Resume[] = (data.resumes || []).map((uploaded: UploadedResumeDB) => ({
            id: uploaded.id,
            file: 'database', // Indicate file is stored in database
            name: uploaded.original_filename || 'Unknown file',
            size: uploaded.file_size ? `${(uploaded.file_size / 1024 / 1024).toFixed(2)} MB` : 'Unknown size',
            status: 'uploaded'
          }))
          console.log('Setting UI resumes:', uiResumes)
          setResumes(uiResumes)

          // Set active tab based on session status (only if not already set)
          if (activeTab === 'upload') { // Only change tab if we're on the default upload tab
            if (data.session.status === 'completed' && data.results.length > 0) {
              setActiveTab('results')
            } else if (data.resumes.length > 0) {
              setActiveTab('criteria')
            }
          }

          // If evaluation is in progress, start polling
          if (data.session.status === 'in_progress') {
            setEvaluating(true)
            // Set start time if not already set (for resumed evaluations)
            if (evaluationStartTime === 0) {
              setEvaluationStartTime(Date.now())
            }
            startStatusPolling(sessionId)
          }
        }
      } else {
        // Session not found or error, remove from localStorage
        localStorage.removeItem(`evaluation-session-${jobId}`)
      }
    } catch (error) {
      console.error('Error loading existing session:', error)
      localStorage.removeItem(`evaluation-session-${jobId}`)
      throw error // Re-throw to be caught by the parent
    }
  }

  const createEvaluationSession = async (criteria: string[]) => {
    try {
      const response = await fetch('http://localhost:8000/api/job-management/evaluation-sessions/create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          job_id: jobId,
          criteria: criteria
        })
      })

      const data = await response.json()
      if (data.success) {
        setSession(data.session)
        // Store session ID for persistence
        localStorage.setItem(`evaluation-session-${jobId}`, data.session_id)
        return data.session_id
      } else {
        throw new Error(data.error || 'Failed to create evaluation session')
      }
    } catch (error) {
      console.error('Error creating evaluation session:', error)
      throw error
    }
  }

  const startStatusPolling = (sessionId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${sessionId}/status/`, {
          credentials: 'include',
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            const taskStatus = data.task_status

            if (taskStatus) {
              const current = taskStatus.current_resume || 0
              const total = taskStatus.total_resumes || 0
              const now = Date.now()

              // Calculate timing metrics
              let averageTimePerResume = 0
              let estimatedTimeRemaining = 0

              if (current > 0 && evaluationStartTime > 0) {
                const elapsedTime = (now - evaluationStartTime) / 1000 // in seconds
                averageTimePerResume = elapsedTime / current
                const remainingResumes = total - current
                estimatedTimeRemaining = averageTimePerResume * remainingResumes
              }

              setEvaluationProgress({
                current,
                total,
                startTime: evaluationStartTime, // Use the actual start time
                estimatedTimeRemaining,
                averageTimePerResume
              })

              if (taskStatus.status === 'completed') {
                setEvaluating(false)
                clearInterval(pollInterval)
                setStatusPolling(null)
                console.log('Evaluation completed, loading results...')
                // Reload session to get results
                try {
                  await loadExistingSession(sessionId)
                  setActiveTab('results')
                } catch (error) {
                  console.error('Error loading results after completion:', error)
                }
              } else if (taskStatus.status === 'failed') {
                setEvaluating(false)
                clearInterval(pollInterval)
                setStatusPolling(null)
                alert(`Evaluation failed: ${taskStatus.error || 'Unknown error'}`)
              }
            }
          }
        }
      } catch (error) {
        console.error('Error polling status:', error)
      }
    }, 2000) // Poll every 2 seconds

    setStatusPolling(pollInterval)
  }



  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showActionsDropdown && !target.closest('.actions-dropdown')) {
        setShowActionsDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showActionsDropdown])

  const fetchJobDetails = async () => {
    try {
      const response = await fetch(`http://localhost:8000/api/job-management/jobs/${jobId}/`, {
        credentials: 'include',
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setJob(data.data)
        } else {
          alert('Failed to load job details')
          // Could call onBack() here if needed
        }
      } else {
        alert('Failed to load job details')
        // Could call onBack() here if needed
      }
    } catch (error) {
      console.error('Error fetching job details:', error)
      alert('Network error occurred')
      // Could call onBack() here if needed
      throw error // Re-throw to be caught by the useEffect
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    // Check for duplicate file names
    const existingFileNames = new Set([
      ...resumes.map(r => r.name),
      ...uploadedResumes.map(r => r.original_filename)
    ])

    const fileArray = Array.from(files)
    const duplicateFiles = fileArray.filter(file => existingFileNames.has(file.name))
    if (duplicateFiles.length > 0) {
      alert(`The following files are already uploaded: ${duplicateFiles.map(f => f.name).join(', ')}`)
      event.target.value = '' // Reset input
      return
    }

    try {
      // Ensure we have a session
      let currentSessionId = session?.id
      if (!currentSessionId) {
        // Create session with default criteria for now, user can change later
        const defaultCriteria = ['skills_match', 'experience_relevance', 'education_fit']
        currentSessionId = await createEvaluationSession(defaultCriteria)
      }

      // Validate file types
      const validFiles: File[] = []
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i]
        const allowedTypes = ['.pdf', '.doc', '.docx', '.txt']
        const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()

        if (!allowedTypes.includes(fileExtension)) {
          alert(`File ${file.name} is not supported. Please upload PDF, DOC, DOCX, or TXT files.`)
          continue
        }
        validFiles.push(file)
      }

      if (validFiles.length === 0) return

      // Upload files to session
      const formData = new FormData()
      formData.append('session_id', currentSessionId!)

      validFiles.forEach((file, index) => {
        formData.append(`resume_${index}`, file)
      })

      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${currentSessionId}/upload/`, {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      const data = await response.json()
      if (data.success) {
        setUploadedResumes(prev => [...prev, ...data.uploaded_resumes])

        // Update session total resumes count
        if (session) {
          setSession(prev => prev ? {...prev, total_resumes: data.total_resumes} : prev)
        }

        // Create display resumes for UI compatibility
        const newResumes: Resume[] = data.uploaded_resumes.map((uploaded: UploadedResumeDB) => ({
          id: uploaded.id,
          file: 'database', // Indicate file is stored in database
          name: uploaded.original_filename,
          size: `${(uploaded.file_size / 1024 / 1024).toFixed(2)} MB`,
          status: 'uploaded'
        }))
        setResumes(prev => [...prev, ...newResumes])

      } else {
        throw new Error(data.error || 'Failed to upload resumes')
      }
    } catch (error) {
      console.error('Error uploading files:', error)
      alert('Failed to upload files. Please try again.')
    }

    event.target.value = '' // Reset input
  }

  const removeResume = async (resumeId: string) => {
    if (!session?.id) return

    try {
      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${session.id}/resumes/${resumeId}/remove/`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          // Remove from UI
          setResumes(prev => prev.filter(r => r.id !== resumeId))
          setUploadedResumes(prev => prev.filter(r => r.id !== resumeId))

          // Check if session was deleted
          if (data.session_deleted) {
            // Clear all session data and redirect to upload tab
            setSession(null)
            setResumes([])
            setUploadedResumes([])
            setResults([])
            setSelectedCriteria(new Set())
            setActiveTab('upload')

            // Remove from localStorage
            localStorage.removeItem(`evaluation-session-${jobId}`)

            console.log('Session deleted due to no remaining resumes')
          } else {
            // Update session total count
            if (session) {
              setSession(prev => prev ? { ...prev, total_resumes: data.total_resumes } : null)
            }
          }
        } else {
          alert('Failed to remove resume: ' + data.error)
        }
      } else {
        alert('Failed to remove resume. Please try again.')
      }
    } catch (error) {
      console.error('Error removing resume:', error)
      alert('Failed to remove resume. Please try again.')
    }
  }

  const clearAllResumes = async () => {
    if (!session?.id) {
      // No session, just clear UI
      setResumes([])
      setUploadedResumes([])
      return
    }

    try {
      // Remove all resumes one by one, which will trigger session deletion
      const resumesToRemove = [...uploadedResumes]
      for (const resume of resumesToRemove) {
        await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${session.id}/resumes/${resume.id}/remove/`, {
          method: 'DELETE',
          credentials: 'include',
        })
      }

      // Clear UI state
      setResumes([])
      setUploadedResumes([])
      setSession(null)
      setResults([])
      setSelectedCriteria(new Set())
      setActiveTab('upload')

      // Remove from localStorage
      localStorage.removeItem(`evaluation-session-${jobId}`)

      console.log('All resumes cleared and session deleted')
    } catch (error) {
      console.error('Error clearing resumes:', error)
      // Still clear UI even if API calls fail
      setResumes([])
      setUploadedResumes([])
    }
  }

  const handleCriteriaToggle = async (criteriaId: string) => {
    const newSelected = new Set(selectedCriteria)
    if (newSelected.has(criteriaId)) {
      newSelected.delete(criteriaId)
    } else {
      newSelected.add(criteriaId)
    }
    setSelectedCriteria(newSelected)

    // Update criteria in database if session exists
    if (session?.id) {
      await updateSessionCriteria(Array.from(newSelected))
    }
  }

  const updateSessionCriteria = async (criteria: string[]) => {
    if (!session?.id) return

    try {
      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${session.id}/update-criteria/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          criteria: criteria
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log('Criteria updated in database:', criteria)
          // Update session state
          setSession(prev => prev ? { ...prev, selected_criteria: criteria } : prev)
        } else {
          console.error('Failed to update criteria:', data.error)
        }
      }
    } catch (error) {
      console.error('Error updating criteria:', error)
    }
  }

  const addCustomCriteria = () => {
    if (!customCriteriaName.trim()) return

    const newCriteria: EvaluationCriteria = {
      id: 'custom_' + Date.now(),
      name: customCriteriaName.trim(),
      isCustom: true
    }

    setCriteria(prev => [...prev, newCriteria])
    setCustomCriteriaName('')
  }

  const removeCustomCriteria = (criteriaId: string) => {
    setCriteria(prev => prev.filter(c => c.id !== criteriaId))
    setSelectedCriteria(prev => {
      const newSet = new Set(prev)
      newSet.delete(criteriaId)
      return newSet
    })
  }







  const startEvaluation = async () => {
    if (selectedCriteria.size < 3) {
      alert('Please select at least 3 criteria for evaluation')
      return
    }

    if (uploadedResumes.length === 0) {
      alert('Please upload at least one resume')
      return
    }

    try {
      // Ensure we have a session
      let currentSessionId = session?.id
      if (!currentSessionId) {
        currentSessionId = await createEvaluationSession(Array.from(selectedCriteria))
      }

      // Start the asynchronous evaluation
      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${currentSessionId}/start/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          session_id: currentSessionId
        })
      })

      const data = await response.json()
      if (data.success) {
        setEvaluating(true)
        const startTime = Date.now()
        setEvaluationStartTime(startTime)
        setEvaluationProgress({
          current: 0,
          total: uploadedResumes.length,
          startTime: startTime,
          estimatedTimeRemaining: 0,
          averageTimePerResume: 0
        })

        // Start polling for status
        startStatusPolling(currentSessionId!)

        // Evaluation started - no popup needed, user can see progress in UI
      } else {
        throw new Error(data.error || 'Failed to start evaluation')
      }
    } catch (error) {
      console.error('Error starting evaluation:', error)
      alert('Failed to start evaluation. Please try again.')
    }
  }

  const exportToCSV = () => {
    if (results.length === 0) {
      alert('No results to export')
      return
    }

    // Get all criteria names and create headers with both scores and comments
    const headers = ['Rank', 'Resume Name', 'Average Score']
    const selectedCriteriaList = Array.from(selectedCriteria)

    selectedCriteriaList.forEach(criteriaId => {
      const criteriaName = criteria.find(c => c.id === criteriaId)?.name || criteriaId
      headers.push(`${criteriaName} Score`)
      headers.push(`${criteriaName} Comment`)
    })

    // Create CSV rows
    const rows = results.map((result, index) => {
      const row = [
        index + 1, // Rank
        result.resumeName || 'Unknown Resume',
        (result.averageScore || 0).toFixed(2)
      ]

      // Add individual scores and comments
      selectedCriteriaList.forEach(criteriaId => {
        row.push((result.scores || {})[criteriaId] || 0)
        const comment = result.comments?.[criteriaId] || 'No comment available'
        row.push(comment)
      })

      return row
    })

    // Convert to CSV format
    const csvContent = [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n')

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `resume-evaluation-${job?.job_title || 'results'}-${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const clearResults = async () => {
    if (!session?.id) return

    try {
      const response = await fetch(`http://localhost:8000/api/job-management/evaluation-sessions/${session.id}/clear-results/`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (response.ok) {
        setResults([])
        setActiveTab('upload')
        // Reset session status to pending
        if (session) {
          setSession({ ...session, status: 'pending' })
        }
      } else {
        alert('Failed to clear results from database')
      }
    } catch (error) {
      console.error('Error clearing results:', error)
      alert('Failed to clear results')
    }
  }

  const shareReport = async () => {
    if (results.length === 0) {
      alert('No results to share')
      return
    }

    // Create report content
    let reportContent = `Resume Evaluation Report\n`
    reportContent += `Job: ${job?.job_title || 'N/A'}\n`
    reportContent += `Date: ${new Date().toLocaleDateString()}\n`
    reportContent += `Total Resumes Evaluated: ${results.length}\n\n`

    reportContent += `Results (Ranked by Score):\n`
    reportContent += `${'='.repeat(50)}\n\n`

    results.forEach((result, index) => {
      reportContent += `${index + 1}. ${result.resumeName || 'Unknown Resume'}\n`
      reportContent += `   Overall Score: ${(result.averageScore || 0).toFixed(2)}/5\n`
      reportContent += `   \n`
      reportContent += `   Detailed Scores:\n`

      Object.entries(result.scores || {}).forEach(([criteriaId, score]) => {
        const criteriaName = criteria.find(c => c.id === criteriaId)?.name || criteriaId
        const comment = result.comments?.[criteriaId] || 'No comment available'
        reportContent += `   • ${criteriaName}: ${score}/5\n`
        reportContent += `     ${comment}\n`
      })
      reportContent += `\n`
    })

    reportContent += `\nGenerated by Talent Hero v3.11`

    // Try to copy to clipboard
    try {
      await navigator.clipboard.writeText(reportContent)
      alert('Report copied to clipboard! You can now paste it into your email or document.')
    } catch (error) {
      // Fallback: Download as text file
      console.error('Clipboard not available, downloading as file:', error)
      const blob = new Blob([reportContent], { type: 'text/plain' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `resume-evaluation-report-${Date.now()}.txt`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      alert('Report downloaded as text file! You can attach it to your email.')
    }
  }



  const formatTime = (seconds: number): string => {
    if (seconds < 60) {
      return `${Math.round(seconds)}s`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = Math.round(seconds % 60)
      return `${minutes}m ${remainingSeconds}s`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}h ${minutes}m`
    }
  }





  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading job details...</p>
        </div>
      </div>
    )
  }

  try {
    return (
      <div className="space-y-6">
        {/* Header */}
        <div>
        <button
          onClick={onBack}
          className="inline-flex items-center px-4 py-2 border border-input rounded-md bg-background text-card-foreground hover:bg-muted mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Job Details
        </button>
        <div>
          <h1 className="text-2xl font-bold text-card-foreground">Bulk Resume Evaluation</h1>
          <p className="text-muted-foreground">
            Job: {job?.job_title}
          </p>
        </div>
      </div>

          {/* Tabs */}
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('upload')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'upload'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-card-foreground hover:border-muted'
                }`}
              >
                <Upload className="h-4 w-4 inline mr-2" />
                Upload Resumes ({resumes.length})
              </button>
              <button
                onClick={() => setActiveTab('criteria')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'criteria'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-card-foreground hover:border-muted'
                }`}
              >
                <CheckCircle className="h-4 w-4 inline mr-2" />
                Criteria ({selectedCriteria.size})
              </button>
              <button
                onClick={() => setActiveTab('results')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'results'
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-card-foreground hover:border-muted'
                }`}
              >
                <BarChart3 className="h-4 w-4 inline mr-2" />
                Results ({results.length})
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          <div className="bg-card rounded-lg border border-border p-6">
            {activeTab === 'upload' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-card-foreground mb-4">Upload Resumes</h3>
                  
                  {/* File Upload Area */}
                  <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                    <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h4 className="text-lg font-medium text-card-foreground mb-2">
                      Drop files here or click to browse
                    </h4>
                    <p className="text-muted-foreground mb-4">
                      Supported formats: PDF, DOC, DOCX
                    </p>
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="resume-upload"
                    />
                    <label
                      htmlFor="resume-upload"
                      className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 cursor-pointer"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Choose Files
                    </label>
                  </div>
                </div>

                {/* Uploaded Resumes List */}
                {resumes.length > 0 && (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="text-md font-medium text-card-foreground">
                        Uploaded Resumes ({resumes.length})
                      </h4>
                      <button
                        onClick={clearAllResumes}
                        className="p-1 rounded hover:bg-muted transition-colors text-muted-foreground hover:text-destructive"
                        title="Clear all resumes"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                    <div className="space-y-2">
                      {resumes.map((resume) => (
                        <div
                          key={resume.id}
                          className={`flex items-center justify-between p-3 rounded-lg ${
                            resume.file ? 'bg-muted/50' : 'bg-yellow-50 border border-yellow-200'
                          }`}
                        >
                          <div className="flex items-center space-x-3">
                            <FileText className={`h-5 w-5 ${
                              resume.file ? 'text-primary' : 'text-yellow-600'
                            }`} />
                            <div>
                              <p className="font-medium text-card-foreground">{resume.name}</p>
                              <p className="text-sm text-muted-foreground">{resume.size}</p>
                              {!resume.file && (
                                <p className="text-xs text-yellow-600">⚠ Needs re-upload after page reload</p>
                              )}
                            </div>
                          </div>
                          <button
                            onClick={() => removeResume(resume.id)}
                            className="p-1 rounded hover:bg-muted transition-colors"
                          >
                            <X className="h-4 w-4 text-muted-foreground" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'criteria' && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-card-foreground mb-2">Evaluation Criteria</h3>
                  <p className="text-muted-foreground mb-4">
                    Select at least 3 criteria for resume evaluation. You can also add custom criteria.
                  </p>
                </div>

                {/* Criteria Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {criteria.map((criterion) => (
                    <div
                      key={criterion.id}
                      className={`relative p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedCriteria.has(criterion.id)
                          ? 'border-primary bg-primary/5'
                          : 'border-border hover:border-muted-foreground'
                      }`}
                      onClick={() => handleCriteriaToggle(criterion.id)}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-card-foreground">{criterion.name}</span>
                        {criterion.isCustom && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              removeCustomCriteria(criterion.id)
                            }}
                            className="p-1 rounded hover:bg-muted transition-colors"
                          >
                            <X className="h-3 w-3 text-muted-foreground" />
                          </button>
                        )}
                      </div>
                      {selectedCriteria.has(criterion.id) && (
                        <CheckCircle className="absolute top-2 right-2 h-4 w-4 text-primary" />
                      )}
                    </div>
                  ))}
                </div>

                {/* Add Custom Criteria */}
                <div className="border-t border-border pt-6">
                  <h4 className="text-md font-medium text-card-foreground mb-4">Add Custom Criteria</h4>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={customCriteriaName}
                      onChange={(e) => setCustomCriteriaName(e.target.value)}
                      placeholder="Enter custom criteria name..."
                      className="flex-1 px-3 py-2 border border-input rounded-md bg-background text-card-foreground"
                      onKeyPress={(e) => e.key === 'Enter' && addCustomCriteria()}
                    />
                    <button
                      onClick={addCustomCriteria}
                      disabled={!customCriteriaName.trim()}
                      className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add
                    </button>
                  </div>
                </div>

                {/* Selected Criteria Summary */}
                {selectedCriteria.size > 0 && (
                  <div className="bg-muted/50 rounded-lg p-4">
                    <h4 className="font-medium text-card-foreground mb-2">
                      Selected Criteria ({selectedCriteria.size})
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {Array.from(selectedCriteria).map((criteriaId) => {
                        const criterion = criteria.find(c => c.id === criteriaId)
                        return (
                          <span
                            key={criteriaId}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary/10 text-primary"
                          >
                            {criterion?.name}
                          </span>
                        )
                      })}
                    </div>
                    {selectedCriteria.size < 3 && (
                      <p className="text-sm text-yellow-600 mt-2">
                        Please select at least {3 - selectedCriteria.size} more criteria
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}

            {activeTab === 'results' && (
              <div className="space-y-6">
                {results.length === 0 ? (
                  <div className="text-center py-12">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-card-foreground mb-2">No Results Yet</h3>
                    <p className="text-muted-foreground mb-4">
                      Upload resumes and select criteria to start evaluation
                    </p>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium text-card-foreground">
                        Evaluation Results (Ranked)
                      </h3>
                      <div className="relative actions-dropdown">
                        <button
                          onClick={() => setShowActionsDropdown(!showActionsDropdown)}
                          className="inline-flex items-center px-4 py-2 border border-input rounded-md bg-background text-card-foreground hover:bg-muted"
                        >
                          <MoreVertical className="h-4 w-4 mr-2" />
                          Actions
                        </button>

                        {showActionsDropdown && (
                          <div className="absolute right-0 mt-2 w-48 bg-background border border-input rounded-md shadow-lg z-10">
                            <div className="py-1">
                              <button
                                onClick={(e) => {
                                  e.preventDefault()
                                  e.stopPropagation()
                                  clearResults()
                                  setShowActionsDropdown(false)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-card-foreground hover:bg-muted"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Clear Results
                              </button>
                              <button
                                onClick={(e) => {
                                  e.preventDefault()
                                  e.stopPropagation()
                                  exportToCSV()
                                  setShowActionsDropdown(false)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-card-foreground hover:bg-muted"
                              >
                                <Download className="h-4 w-4 mr-2" />
                                Export CSV
                              </button>
                              <button
                                onClick={(e) => {
                                  e.preventDefault()
                                  e.stopPropagation()
                                  shareReport()
                                  setShowActionsDropdown(false)
                                }}
                                className="flex items-center w-full px-4 py-2 text-sm text-card-foreground hover:bg-muted"
                              >
                                <Mail className="h-4 w-4 mr-2" />
                                Share Report
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      {results.map((result, index) => (
                        <div
                          key={result.resumeId || `result-${index}`}
                          className="p-4 border border-border rounded-lg"
                        >
                          <div className="flex items-center justify-between mb-3">
                            <div className="flex items-center space-x-3">
                              <span className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground font-bold">
                                {index + 1}
                              </span>
                              <div>
                                <h4 className="font-medium text-card-foreground">{result.resumeName || 'Unknown Resume'}</h4>
                                <p className="text-sm text-muted-foreground">
                                  Average Score: {(result.averageScore || 0).toFixed(1)}/5.0
                                </p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {Object.entries(result.scores || {}).map(([criteriaId, score]) => {
                              const criterion = criteria.find(c => c.id === criteriaId)
                              const comment = result.comments?.[criteriaId] || 'No comment available'
                              return (
                                <div key={criteriaId} className="p-3 bg-muted/30 rounded-lg">
                                  <div className="flex items-center justify-between mb-2">
                                    <p className="text-sm font-medium text-card-foreground">{criterion?.name}</p>
                                    <div className="flex items-center">
                                      <span className="text-lg font-bold text-card-foreground">{score}</span>
                                      <span className="text-sm text-muted-foreground">/5</span>
                                    </div>
                                  </div>
                                  <p className="text-xs text-muted-foreground italic">{comment}</p>
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

        {/* Evaluation Progress */}
        {evaluating && evaluationProgress.total > 0 && (
          <div className="bg-muted/30 border border-border rounded-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-card-foreground">Evaluation Progress</h3>
              <div className="text-sm text-muted-foreground">
                {evaluationProgress.current} of {evaluationProgress.total} resumes processed
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-muted rounded-full h-2 mb-4">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{
                  width: `${(evaluationProgress.current / evaluationProgress.total) * 100}%`
                }}
              />
            </div>

            {/* Time Information */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Elapsed Time:</span>
                <div className="font-medium text-card-foreground">
                  {evaluationStartTime > 0 ? formatTime((Date.now() - evaluationStartTime) / 1000) : '0s'}
                </div>
              </div>

              {evaluationProgress.averageTimePerResume > 0 && (
                <div>
                  <span className="text-muted-foreground">Avg. Time per Resume:</span>
                  <div className="font-medium text-card-foreground">
                    {formatTime(evaluationProgress.averageTimePerResume)}
                  </div>
                </div>
              )}

              {evaluationProgress.estimatedTimeRemaining > 0 && evaluationProgress.current < evaluationProgress.total && (
                <div>
                  <span className="text-muted-foreground">Est. Time Remaining:</span>
                  <div className="font-medium text-card-foreground">
                    {formatTime(evaluationProgress.estimatedTimeRemaining)}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {activeTab !== 'results' && (
          <div className="flex justify-end space-x-3">
            <button
              onClick={startEvaluation}
              disabled={evaluating || uploadedResumes.length === 0 || selectedCriteria.size < 3}
              className="inline-flex items-center px-6 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 disabled:opacity-50"
            >
              <Play className="h-4 w-4 mr-2" />
              {evaluating ? 'Evaluating...' : 'Start Evaluation'}
            </button>
          </div>
        )}
      </div>
    )
  } catch (error) {
    console.error('Error rendering BulkResumeEvaluation:', error)
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-4">Something went wrong</h2>
          <p className="text-muted-foreground mb-4">There was an error loading the bulk resume evaluation.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }
}
