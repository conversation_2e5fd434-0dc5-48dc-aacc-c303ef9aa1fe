import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { UserCheck, Plus, Search, ArrowLeft, MoreVertical, ChevronDown, RefreshCw } from 'lucide-react'
import CreateCandidateModal from './CreateCandidateModal'

interface Candidate {
  id: string
  full_name: string
  email: string
  phone_number: string
  preferred_role: string
  total_experience: string
  last_job_date: string
  status: string
  created_at: string
}

interface PaginationData {
  current_page: number
  total_pages: number
  total_count: number
  has_next: boolean
  has_previous: boolean
}

interface CandidateManagementProps {
  onBack?: () => void
}

export default function CandidateManagement({ onBack }: CandidateManagementProps) {
  const [candidates, setCandidates] = useState<Candidate[]>([])
  const [pagination, setPagination] = useState<PaginationData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingCandidateId, setEditingCandidateId] = useState<string | null>(null)
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [showActionsDropdown, setShowActionsDropdown] = useState(false)
  const [syncing, setSyncing] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    fetchCandidates()
  }, [currentPage, searchTerm])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showActionsDropdown) {
        setShowActionsDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showActionsDropdown])

  const fetchCandidates = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        per_page: '20'
      })

      if (searchTerm) {
        params.append('search', searchTerm)
      }

      const response = await fetch(`http://localhost:8000/api/candidate-management/candidates/?${params}`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setCandidates(data.data)
          setPagination(data.pagination)
        } else {
          setError(data.error || 'Failed to fetch candidates')
        }
      } else {
        setError('Failed to fetch candidates')
      }
    } catch (error) {
      console.error('Error fetching candidates:', error)
      setError('Failed to fetch candidates')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchCandidates()
  }

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage)
    // Scroll to bottom to keep pagination visible
    setTimeout(() => {
      window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' })
    }, 100)
  }

  const handleSyncFromZoho = async () => {
    setSyncing(true)
    try {
      const response = await fetch('http://localhost:8000/api/candidate-management/candidates/sync-from-zoho/', {
        method: 'POST',
        credentials: 'include',
      })

      if (response.ok) {
        const result = await response.json()

        if (result.success) {
          alert(`Candidate sync completed successfully!\n\nCreated: ${result.data.created} candidates\nUpdated: ${result.data.updated} candidates\nSkipped: ${result.data.skipped} candidates\nTotal processed: ${result.data.total_processed} candidates`)
          fetchCandidates() // Refresh the candidate list
        } else {
          alert(`Sync failed: ${result.error || 'Unknown error occurred'}`)
        }
      } else {
        // Handle HTTP errors
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`
        try {
          const errorResult = await response.json()
          if (errorResult.error) {
            errorMessage = errorResult.error
          }
        } catch (e) {
          // If response is not JSON, use status text
        }
        alert(`Sync failed: ${errorMessage}`)
      }
    } catch (error) {
      console.error('Error syncing from Zoho:', error)
      alert('Network error while syncing from Zoho')
    } finally {
      setSyncing(false)
    }
  }

  const handleCreateCandidate = () => {
    setEditingCandidateId(null)
    setIsCreateModalOpen(true)
  }

  const handleCandidateCreated = () => {
    fetchCandidates()
  }

  const handleModalClose = () => {
    setIsCreateModalOpen(false)
    setEditingCandidateId(null)
  }

  const handleEditCandidate = (candidateId: string) => {
    setEditingCandidateId(candidateId)
    setIsCreateModalOpen(true)
  }

  const handleDeleteCandidate = async (candidateId: string, candidateName: string) => {
    if (window.confirm(`Are you sure you want to delete candidate "${candidateName}"?`)) {
      try {
        const response = await fetch(`http://localhost:8000/api/candidate-management/candidates/${candidateId}/delete/`, {
          method: 'DELETE',
          credentials: 'include',
        })

        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            fetchCandidates() // Refresh the list
            // Remove from selected candidates if it was selected
            setSelectedCandidates(prev => prev.filter(id => id !== candidateId))
          } else {
            setError(data.error || 'Failed to delete candidate')
          }
        } else {
          setError('Failed to delete candidate')
        }
      } catch (error) {
        console.error('Error deleting candidate:', error)
        setError('Network error while deleting candidate')
      }
    }
  }

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked)
    if (checked) {
      setSelectedCandidates(candidates.map(candidate => candidate.id))
    } else {
      setSelectedCandidates([])
    }
  }

  const handleSelectCandidate = (candidateId: string, checked: boolean) => {
    if (checked) {
      setSelectedCandidates(prev => [...prev, candidateId])
    } else {
      setSelectedCandidates(prev => prev.filter(id => id !== candidateId))
      setSelectAll(false)
    }
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header with back button */}
      <div className="flex flex-col space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {onBack && (
              <button
                onClick={onBack}
                className="mr-4 p-2 rounded-md hover:bg-muted transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
            )}
            <div>
              <h2 className="text-2xl font-bold text-card-foreground flex items-center">
                <UserCheck className="h-6 w-6 mr-2" />
                Candidates
              </h2>
              <p className="text-muted-foreground">Manage candidate profiles and applications</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Actions Dropdown */}
            <div className="relative">
              <button
                onClick={() => setShowActionsDropdown(!showActionsDropdown)}
                disabled={selectedCandidates.length === 0}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
              >
                <MoreVertical className="h-4 w-4 mr-2" />
                Actions ({selectedCandidates.length})
              </button>
              {showActionsDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-popover border border-border rounded-md shadow-lg z-50">
                  <div className="py-1">
                    <button
                      onClick={() => {
                        console.log('Bulk delete selected candidates:', selectedCandidates)
                        alert(`Bulk delete functionality will be implemented. Selected: ${selectedCandidates.length} candidates`)
                        setShowActionsDropdown(false)
                      }}
                      disabled={selectedCandidates.length === 0}
                      className="w-full text-left px-4 py-2 text-sm text-popover-foreground hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Delete Selected ({selectedCandidates.length})
                    </button>
                    <button
                      onClick={() => {
                        console.log('Bulk export selected candidates:', selectedCandidates)
                        alert(`Bulk export functionality will be implemented. Selected: ${selectedCandidates.length} candidates`)
                        setShowActionsDropdown(false)
                      }}
                      disabled={selectedCandidates.length === 0}
                      className="w-full text-left px-4 py-2 text-sm text-popover-foreground hover:bg-muted disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Export Selected ({selectedCandidates.length})
                    </button>
                  </div>
                </div>
              )}
            </div>
            <button
              onClick={handleSyncFromZoho}
              disabled={syncing}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
              {syncing ? 'Syncing...' : 'Sync from Zoho'}
            </button>
            <button
              onClick={handleCreateCandidate}
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Candidate
            </button>
          </div>
        </div>

        {/* Search */}
        <form onSubmit={handleSearch} className="flex w-full max-w-sm items-center space-x-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search candidates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          <button
            type="submit"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
          >
            Search
          </button>
        </form>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-destructive/15 text-destructive p-4 rounded-md">
          {error}
        </div>
      )}

      {/* Candidates Table */}
      <div className="bg-card rounded-lg border border-border">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="rounded border-input"
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Name</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Email</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Phone</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Preferred Role</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Total Experience</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Last Job Date</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-muted-foreground">Actions</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan={9} className="px-4 py-8 text-center text-muted-foreground">
                    Loading candidates...
                  </td>
                </tr>
              ) : candidates.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-8 text-center">
                    <UserCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-card-foreground mb-2">No candidates found</h3>
                    <p className="text-muted-foreground mb-4">
                      {searchTerm ? 'No candidates match your search criteria.' : 'Get started by creating your first candidate profile.'}
                    </p>
                    <button
                      onClick={handleCreateCandidate}
                      className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Candidate
                    </button>
                  </td>
                </tr>
              ) : (
                candidates.map((candidate) => (
                  <tr key={candidate.id} className="border-b border-border hover:bg-muted/50">
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        checked={selectedCandidates.includes(candidate.id)}
                        onChange={(e) => handleSelectCandidate(candidate.id, e.target.checked)}
                        className="rounded border-input"
                      />
                    </td>
                    <td className="px-4 py-4">
                      <span className="font-medium text-card-foreground">{candidate.full_name}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{candidate.email || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{candidate.phone_number || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{(candidate as any).preferred_role_name || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{candidate.total_experience || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className="text-sm text-muted-foreground">{candidate.last_job_date || 'N/A'}</span>
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        candidate.status === 'Active' ? 'bg-green-100 text-green-800' :
                        candidate.status === 'Hired' ? 'bg-blue-100 text-blue-800' :
                        candidate.status === 'On-Hold' ? 'bg-yellow-100 text-yellow-800' :
                        candidate.status === 'Rejected' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {candidate.status}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEditCandidate(candidate.id)}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Edit candidate"
                        >
                          <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => handleDeleteCandidate(candidate.id, candidate.full_name)}
                          className="p-1 rounded hover:bg-muted transition-colors"
                          title="Delete candidate"
                        >
                          <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination && pagination.total_pages > 1 && (
          <div className="flex items-center justify-between px-4 py-4 border-t border-border">
            <div className="text-sm text-muted-foreground">
              Showing {candidates.length} of {pagination.total_count} candidates
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={!pagination.has_previous}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Previous
              </button>
              <span className="text-sm text-muted-foreground">
                Page {pagination.current_page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={!pagination.has_next}
                className="px-3 py-1 rounded border border-input text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-muted transition-colors"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Candidate Modal */}
      <CreateCandidateModal
        isOpen={isCreateModalOpen}
        onClose={handleModalClose}
        onCandidateCreated={handleCandidateCreated}
        candidateId={editingCandidateId || undefined}
        isEditing={!!editingCandidateId}
      />
    </div>
  )
}
