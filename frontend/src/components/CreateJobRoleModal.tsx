import { useState } from 'react'
import { X, Briefcase, Upload, FileText } from 'lucide-react'

interface CreateJobRoleModalProps {
  isOpen: boolean
  onClose: () => void
  onJobRoleCreated: () => void
}

export default function CreateJobRoleModal({ isOpen, onClose, onJobRoleCreated }: CreateJobRoleModalProps) {
  const [roleName, setRoleName] = useState('')
  const [yearsOfExperience, setYearsOfExperience] = useState('')
  const [jobDescription, setJobDescription] = useState('')
  const [descriptionType, setDescriptionType] = useState<'text' | 'file'>('text')
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')

  const resetForm = () => {
    setRoleName('')
    setYearsOfExperience('')
    setJobDescription('')
    setDescriptionType('text')
    setSelectedFile(null)
    setError('')
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Check file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a PDF, DOC, DOCX, or TXT file')
        return
      }
      
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('File size must be less than 5MB')
        return
      }
      
      setSelectedFile(file)
      setError('')
    }
  }

  const createJobRole = async () => {
    if (!roleName.trim()) {
      setError('Role name is required')
      return
    }

    if (!yearsOfExperience.trim()) {
      setError('Years of experience is required')
      return
    }

    const years = parseInt(yearsOfExperience)
    if (isNaN(years) || years < 0) {
      setError('Years of experience must be a valid non-negative number')
      return
    }

    if (descriptionType === 'text' && !jobDescription.trim()) {
      setError('Job description is required when using text input')
      return
    }

    if (descriptionType === 'file' && !selectedFile) {
      setError('Please select a file when using file upload')
      return
    }

    setCreating(true)
    try {
      let requestData: any = {
        role_name: roleName.trim(),
        years_of_experience: years,
        description_type: descriptionType
      }

      if (descriptionType === 'text') {
        requestData.job_description = jobDescription.trim()
      }

      // For now, we'll only handle text input. File upload will be implemented later
      if (descriptionType === 'file') {
        setError('File upload functionality will be implemented soon. Please use text input for now.')
        setCreating(false)
        return
      }

      const response = await fetch('http://localhost:8000/api/job-management/job-roles/create/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          handleClose()
          onJobRoleCreated()
        } else {
          setError(data.error || 'Failed to create job role')
        }
      } else {
        setError('Failed to create job role')
      }
    } catch (error) {
      setError('Network error')
    } finally {
      setCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-card rounded-lg border border-border p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-card-foreground flex items-center">
            <Briefcase className="h-5 w-5 mr-2" />
            Create Job Role
          </h3>
          <button
            onClick={handleClose}
            className="p-1 rounded hover:bg-muted transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-destructive/15 text-destructive rounded-md">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <div>
            <label htmlFor="roleName" className="block text-sm font-medium mb-1">
              Role Name <span className="text-destructive">*</span>
            </label>
            <input
              id="roleName"
              type="text"
              value={roleName}
              onChange={(e) => setRoleName(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="e.g., Senior Software Engineer, Marketing Manager"
            />
          </div>

          <div>
            <label htmlFor="yearsOfExperience" className="block text-sm font-medium mb-1">
              Years of Experience <span className="text-destructive">*</span>
            </label>
            <input
              id="yearsOfExperience"
              type="number"
              min="0"
              value={yearsOfExperience}
              onChange={(e) => setYearsOfExperience(e.target.value)}
              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              Job Description <span className="text-destructive">*</span>
            </label>
            
            {/* Description Type Toggle */}
            <div className="flex space-x-4 mb-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="descriptionType"
                  value="text"
                  checked={descriptionType === 'text'}
                  onChange={(e) => setDescriptionType(e.target.value as 'text' | 'file')}
                  className="mr-2"
                />
                <FileText className="h-4 w-4 mr-1" />
                Text Input
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="descriptionType"
                  value="file"
                  checked={descriptionType === 'file'}
                  onChange={(e) => setDescriptionType(e.target.value as 'text' | 'file')}
                  className="mr-2"
                />
                <Upload className="h-4 w-4 mr-1" />
                File Upload
              </label>
            </div>

            {descriptionType === 'text' ? (
              <textarea
                value={jobDescription}
                onChange={(e) => setJobDescription(e.target.value)}
                className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                placeholder="Enter detailed job description, responsibilities, requirements..."
                rows={6}
              />
            ) : (
              <div>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx,.txt"
                  onChange={handleFileChange}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Supported formats: PDF, DOC, DOCX, TXT (max 5MB)
                </p>
                {selectedFile && (
                  <div className="mt-2 p-2 bg-muted rounded-md">
                    <p className="text-sm text-card-foreground">
                      Selected: {selectedFile.name} ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-2">
          <button
            onClick={handleClose}
            className="px-4 py-2 text-sm border border-input rounded-md hover:bg-muted transition-colors"
            disabled={creating}
          >
            Cancel
          </button>
          <button
            onClick={createJobRole}
            disabled={creating || !roleName.trim() || !yearsOfExperience.trim()}
            className="px-4 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? 'Creating...' : 'Create Job Role'}
          </button>
        </div>
      </div>
    </div>
  )
}
