import { useState, useEffect } from 'react'
import { X, Upload, User, Phone, Mail, Briefcase, Calendar, FileText, MessageSquare } from 'lucide-react'
import { DatePicker } from '@/components/ui/date-picker'
import { format } from 'date-fns'

interface JobRole {
  id: string
  role_name: string
  years_of_experience: number
}

interface InterviewRound {
  round_name: string
  status: string
  schedule_date: string
  schedule_time: string
  panel_name: string
  panel_comment: string
  score: string
}

interface CreateCandidateModalProps {
  isOpen: boolean
  onClose: () => void
  onCandidateCreated: () => void
  candidateId?: string // For editing existing candidate
  isEditing?: boolean
}

export default function CreateCandidateModal({ isOpen, onClose, onCandidateCreated, candidateId, isEditing = false }: CreateCandidateModalProps) {
  // Form state
  const [fullName, setFullName] = useState('')
  const [phoneNumber, setPhoneNumber] = useState('')
  const [email, setEmail] = useState('')
  const [candidateIdField, setCandidateIdField] = useState('')
  const [city, setCity] = useState('')
  const [applicationType, setApplicationType] = useState('')
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null)
  const [profilePhotoPreview, setProfilePhotoPreview] = useState<string | null>(null)
  const [preferredRole, setPreferredRole] = useState('')
  const [optionalRoles, setOptionalRoles] = useState<string[]>([])
  const [totalExperience, setTotalExperience] = useState('')
  const [lastJobDate, setLastJobDate] = useState<Date | undefined>()
  const [resumeFile, setResumeFile] = useState<File | null>(null)
  const [comments, setComments] = useState('')
  
  // Interview rounds state
  const [interviewRounds, setInterviewRounds] = useState<InterviewRound[]>([
    {
      round_name: 'L1 Round',
      status: 'Scheduled',
      schedule_date: '',
      schedule_time: '',
      panel_name: '',
      panel_comment: '',
      score: ''
    }
  ])
  
  // Bulk upload state
  const [bulkFile, setBulkFile] = useState<File | null>(null)
  const [activeTab, setActiveTab] = useState<'bulk' | 'form'>('form')
  
  // Other state
  const [jobRoles, setJobRoles] = useState<JobRole[]>([])
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')
  const [expandedRounds, setExpandedRounds] = useState<{ [key: string]: boolean }>({ 'L1 Round': true })

  useEffect(() => {
    if (isOpen) {
      fetchJobRoles()
      if (isEditing && candidateId) {
        fetchCandidateDetails()
      } else {
        resetForm()
      }
    }
  }, [isOpen, isEditing, candidateId])

  const resetForm = () => {
    setFullName('')
    setPhoneNumber('')
    setEmail('')
    setCandidateIdField('')
    setCity('')
    setApplicationType('')
    setProfilePhoto(null)
    setProfilePhotoPreview(null)
    setPreferredRole('')
    setOptionalRoles([])
    setTotalExperience('')
    setLastJobDate(undefined)
    setResumeFile(null)
    setComments('')
    setInterviewRounds([{
      round_name: 'L1 Round',
      status: 'Scheduled',
      schedule_date: '',
      schedule_time: '',
      panel_name: '',
      panel_comment: '',
      score: ''
    }])
    setBulkFile(null)
    setActiveTab('form')
    setError('')
    setExpandedRounds({ 'L1 Round': true })
  }

  const fetchJobRoles = async () => {
    setLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/job-management/job-roles/', {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data && Array.isArray(data.data.job_roles)) {
          setJobRoles(data.data.job_roles)
        } else {
          console.warn('Job roles data is not an array:', data)
          setJobRoles([])
        }
      } else {
        console.error('Failed to fetch job roles:', response.status)
        setJobRoles([])
      }
    } catch (error) {
      console.error('Error fetching job roles:', error)
      setJobRoles([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  const fetchCandidateDetails = async () => {
    if (!candidateId) return

    setLoading(true)
    try {
      const response = await fetch(`http://localhost:8000/api/candidate-management/candidates/${candidateId}/`, {
        credentials: 'include',
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          const candidate = data.data
          // Populate form with candidate data
          setFullName(candidate.full_name || '')
          setPhoneNumber(candidate.phone_number || '')
          setEmail(candidate.email || '')
          setCandidateIdField(candidate.candidate_id || '')
          setCity(candidate.city || '')
          setApplicationType(candidate.application_type || '')
          setTotalExperience(candidate.total_experience || '')
          setLastJobDate(candidate.last_job_date || '')
          setComments(candidate.comments || '')
          setPreferredRole(candidate.preferred_role || '')
          setOptionalRoles(candidate.optional_roles || [])

          // Set profile photo preview if exists
          if (candidate.has_profile_photo) {
            setProfilePhotoPreview(`http://localhost:8000/api/candidate-management/candidates/${candidateId}/profile-photo/`)
          } else {
            setProfilePhotoPreview(null)
          }

          // Set interview rounds
          if (candidate.interview_rounds && Array.isArray(candidate.interview_rounds)) {
            const rounds: InterviewRound[] = candidate.interview_rounds.map((round: any) => ({
              round_name: round.round_name || '',
              status: round.status || 'Scheduled',
              schedule_date: round.schedule_date || '',
              schedule_time: round.schedule_time || '',
              panel_name: round.panel_name || '',
              panel_comment: round.panel_comment || '',
              score: round.score || ''
            }))
            setInterviewRounds(rounds)
          }
        }
      } else {
        console.error('Failed to fetch candidate details:', response.status)
        setError('Failed to load candidate details')
      }
    } catch (error) {
      console.error('Error fetching candidate details:', error)
      setError('Failed to load candidate details')
    } finally {
      setLoading(false)
    }
  }

  const handleOptionalRoleChange = (roleId: string, checked: boolean) => {
    if (checked) {
      if (roleId !== preferredRole && !optionalRoles.includes(roleId)) {
        setOptionalRoles([...optionalRoles, roleId])
      }
    } else {
      setOptionalRoles(optionalRoles.filter(id => id !== roleId))
    }
  }

  const handleProfilePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setProfilePhoto(file)
      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        setProfilePhotoPreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleInterviewRoundChange = (index: number, field: string, value: string) => {
    const updatedRounds = [...interviewRounds]
    updatedRounds[index] = { ...updatedRounds[index], [field]: value }
    setInterviewRounds(updatedRounds)
  }

  const toggleRoundExpansion = (roundName: string) => {
    setExpandedRounds(prev => ({
      ...prev,
      [roundName]: !prev[roundName]
    }))
  }

  const addInterviewRound = (roundName: string) => {
    const newRound: InterviewRound = {
      round_name: roundName,
      status: 'Scheduled',
      schedule_date: '',
      schedule_time: '',
      panel_name: '',
      panel_comment: '',
      score: ''
    }
    setInterviewRounds([...interviewRounds, newRound])
    setExpandedRounds(prev => ({ ...prev, [roundName]: true }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('Form submitted')

    if (!fullName.trim()) {
      setError('Full name is required')
      return
    }

    setCreating(true)
    try {
      // Use FormData to handle file uploads
      const formData = new FormData()
      formData.append('full_name', fullName.trim())
      formData.append('phone_number', phoneNumber.trim())
      formData.append('email', email.trim())
      formData.append('candidate_id', candidateIdField.trim())
      formData.append('city', city.trim())
      formData.append('application_type', applicationType)
      formData.append('preferred_role', preferredRole)
      formData.append('optional_roles', JSON.stringify(optionalRoles))
      formData.append('total_experience', totalExperience.trim())
      formData.append('last_job_date', lastJobDate ? format(lastJobDate, 'yyyy-MM-dd') : '')
      formData.append('comments', comments.trim())
      formData.append('interview_rounds', JSON.stringify(interviewRounds))

      // Add profile photo if selected
      if (profilePhoto) {
        formData.append('profile_photo', profilePhoto)
      }

      const url = isEditing && candidateId
        ? `http://localhost:8000/api/candidate-management/candidates/${candidateId}/update/`
        : 'http://localhost:8000/api/candidate-management/candidates/create/'

      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        credentials: 'include',
        body: formData // Don't set Content-Type header, let browser set it for FormData
      })

      const result = await response.json()

      if (result.success) {
        onCandidateCreated()
        onClose()
      } else {
        setError(result.error || `Failed to ${isEditing ? 'update' : 'create'} candidate`)
      }
    } catch (error) {
      console.error('Error creating candidate:', error)
      setError('Failed to create candidate')
    } finally {
      setCreating(false)
    }
  }

  const handleBulkUpload = async (e: React.FormEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('Bulk upload submitted')

    if (!bulkFile) {
      setError('Please select a file to upload')
      return
    }

    setCreating(true)
    try {
      const formData = new FormData()
      formData.append('file', bulkFile)

      const response = await fetch('http://localhost:8000/api/candidate-management/candidates/bulk-upload/', {
        method: 'POST',
        credentials: 'include',
        body: formData
      })

      const result = await response.json()

      if (result.success) {
        onCandidateCreated()
        onClose()
      } else {
        setError(result.error || 'Failed to upload candidates')
      }
    } catch (error) {
      console.error('Error uploading candidates:', error)
      setError('Failed to upload candidates')
    } finally {
      setCreating(false)
    }
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        // Only close if clicking the backdrop, not the modal content
        if (e.target === e.currentTarget) {
          onClose()
        }
      }}
    >
      <div
        className="bg-card rounded-lg shadow-xl w-full max-w-6xl h-[85vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-card-foreground flex items-center">
            <User className="h-5 w-5 mr-2" />
            {isEditing ? 'Edit Candidate' : 'Create Candidate'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-muted rounded-md transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {error && (
          <div className="mx-6 mt-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-destructive text-sm">{error}</p>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 flex overflow-hidden min-h-0">
          {/* Left Section - Bulk Upload */}
          <div className="w-1/3 border-r border-border p-4 overflow-y-auto flex-shrink-0">
            <h3 className="text-lg font-medium text-card-foreground mb-4 flex items-center">
              <Upload className="h-4 w-4 mr-2" />
              Bulk Upload
            </h3>
            
            <form onSubmit={handleBulkUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Upload Excel/CSV File
                </label>
                <input
                  type="file"
                  accept=".xlsx,.xls,.csv"
                  onChange={(e) => setBulkFile(e.target.files?.[0] || null)}
                  className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Supported formats: .xlsx, .xls, .csv
                </p>
              </div>

              <div className="bg-muted/50 rounded-md p-4">
                <h4 className="text-sm font-medium mb-2">Required Columns:</h4>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• full_name (required)</li>
                  <li>• phone_number</li>
                  <li>• email</li>
                  <li>• candidate_id</li>
                  <li>• preferred_role</li>
                  <li>• total_experience</li>
                  <li>• last_job_date</li>
                  <li>• comments</li>
                </ul>
              </div>

              <button
                type="submit"
                disabled={creating || !bulkFile}
                className="w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
              >
                {creating ? 'Uploading...' : 'Upload Candidates'}
              </button>
            </form>
          </div>

          {/* Right Section - Manual Form */}
          <div className="flex-1 p-4 overflow-y-auto min-w-0">
            <h3 className="text-lg font-medium text-card-foreground mb-4 flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              Manual Entry
            </h3>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="fullName" className="block text-sm font-medium mb-1">
                    Full Name <span className="text-destructive">*</span>
                  </label>
                  <input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter full name"
                  />
                </div>

                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium mb-1">
                    <Phone className="h-4 w-4 inline mr-1" />
                    Phone Number
                  </label>
                  <input
                    id="phoneNumber"
                    type="tel"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter phone number"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-1">
                    <Mail className="h-4 w-4 inline mr-1" />
                    Email
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter email address"
                  />
                </div>

                <div>
                  <label htmlFor="candidateId" className="block text-sm font-medium mb-1">
                    Candidate ID
                  </label>
                  <input
                    id="candidateId"
                    type="text"
                    value={candidateIdField}
                    onChange={(e) => setCandidateIdField(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter candidate ID"
                  />
                </div>

                <div>
                  <label htmlFor="city" className="block text-sm font-medium mb-1">
                    City
                  </label>
                  <input
                    id="city"
                    type="text"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Enter city"
                  />
                </div>

                <div>
                  <label htmlFor="applicationType" className="block text-sm font-medium mb-1">
                    Application Type <span className="text-muted-foreground">(Optional)</span>
                  </label>
                  <select
                    id="applicationType"
                    value={applicationType}
                    onChange={(e) => setApplicationType(e.target.value)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  >
                    <option value="">Select application type</option>
                    <option value="Job Portal">Job Portal</option>
                    <option value="Employee Referral">Employee Referral</option>
                    <option value="Vendor">Vendor</option>
                  </select>
                </div>
              </div>

              {/* Profile Photo */}
              <div className="space-y-4">
                <div>
                  <label htmlFor="profilePhoto" className="block text-sm font-medium mb-1">
                    Profile Photo <span className="text-muted-foreground">(Optional)</span>
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      id="profilePhoto"
                      type="file"
                      accept="image/*"
                      onChange={handleProfilePhotoChange}
                      className="hidden"
                    />
                    <label
                      htmlFor="profilePhoto"
                      className="cursor-pointer inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-muted hover:text-accent-foreground h-10 px-4 py-2"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Choose Photo
                    </label>
                    {profilePhotoPreview && (
                      <div className="flex items-center space-x-2">
                        <img
                          src={profilePhotoPreview}
                          alt="Profile preview"
                          className="h-16 w-16 rounded-full object-cover border border-border"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setProfilePhoto(null)
                            setProfilePhotoPreview(null)
                          }}
                          className="text-sm text-muted-foreground hover:text-destructive"
                        >
                          Remove
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Job Related Information */}
              <div className="space-y-4">
                <div>
                  <label htmlFor="preferredRole" className="block text-sm font-medium mb-1">
                    <Briefcase className="h-4 w-4 inline mr-1" />
                    Preferred Role
                  </label>
                  <select
                    id="preferredRole"
                    value={preferredRole}
                    onChange={(e) => {
                      setPreferredRole(e.target.value)
                      // Remove from optional roles if selected as preferred
                      setOptionalRoles(optionalRoles.filter(id => id !== e.target.value))
                    }}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  >
                    <option value="">Select preferred role</option>
                    {Array.isArray(jobRoles) && jobRoles.map((role) => (
                      <option key={role.id} value={role.id}>
                        {role.role_name} ({role.years_of_experience} years exp.)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Optional Roles (Multi-select)
                  </label>
                  <select
                    multiple
                    value={optionalRoles}
                    onChange={(e) => {
                      const selectedValues = Array.from(e.target.selectedOptions, option => option.value)
                      setOptionalRoles(selectedValues)
                    }}
                    className="w-full h-32 rounded-md border border-input bg-background px-3 py-2 text-sm"
                    size={6}
                  >
                    {Array.isArray(jobRoles) && jobRoles.length > 0 ? (
                      jobRoles
                        .filter(role => role.id !== preferredRole)
                        .map((role) => (
                          <option key={role.id} value={role.id}>
                            {role.role_name} ({role.years_of_experience} years exp.)
                          </option>
                        ))
                    ) : (
                      <option disabled>No job roles available</option>
                    )}
                  </select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Hold Ctrl (Cmd on Mac) to select multiple roles
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="totalExperience" className="block text-sm font-medium mb-1">
                      Total Experience
                    </label>
                    <input
                      id="totalExperience"
                      type="text"
                      value={totalExperience}
                      onChange={(e) => setTotalExperience(e.target.value)}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                      placeholder="e.g., 3.5 years, 2-3 years"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1">
                      <Calendar className="h-4 w-4 inline mr-1" />
                      Last Job Date
                    </label>
                    <DatePicker
                      date={lastJobDate}
                      onDateChange={setLastJobDate}
                      placeholder="Select last job date"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="resumeFile" className="block text-sm font-medium mb-1">
                    Resume Upload
                  </label>
                  <input
                    id="resumeFile"
                    type="file"
                    accept=".pdf,.doc,.docx"
                    onChange={(e) => setResumeFile(e.target.files?.[0] || null)}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Supported formats: PDF, DOC, DOCX
                  </p>
                </div>

                <div>
                  <label htmlFor="comments" className="block text-sm font-medium mb-1">
                    <MessageSquare className="h-4 w-4 inline mr-1" />
                    Comments
                  </label>
                  <textarea
                    id="comments"
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    rows={3}
                    className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                    placeholder="Additional comments or notes"
                  />
                </div>
              </div>

              {/* Interview Rounds Section - This will be continued in the next part */}
              <div className="space-y-4">
                <h4 className="text-md font-medium text-card-foreground">Interview Rounds</h4>
                
                {/* L1 Round (always visible) */}
                {interviewRounds.map((round, index) => (
                  <div key={`${round.round_name}-${index}`} className="border border-border rounded-md">
                    <button
                      type="button"
                      onClick={() => toggleRoundExpansion(round.round_name)}
                      className="w-full flex items-center justify-between p-3 text-left hover:bg-muted/50 transition-colors"
                    >
                      <span className="font-medium">{round.round_name}</span>
                      <span className="text-sm text-muted-foreground">
                        {expandedRounds[round.round_name] ? '−' : '+'}
                      </span>
                    </button>
                    
                    {expandedRounds[round.round_name] && (
                      <div className="p-3 border-t border-border space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <label className="block text-sm font-medium mb-1">Status</label>
                            <select
                              value={round.status}
                              onChange={(e) => handleInterviewRoundChange(index, 'status', e.target.value)}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            >
                              <option value="Scheduled">Scheduled</option>
                              <option value="Attended">Attended</option>
                              <option value="Hold">Hold</option>
                              <option value="Rejected">Rejected</option>
                              <option value="Selected">Selected</option>
                              <option value="Dropped-Out">Dropped-Out</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-1">Schedule Date</label>
                            <input
                              type="date"
                              value={round.schedule_date}
                              onChange={(e) => handleInterviewRoundChange(index, 'schedule_date', e.target.value)}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-1">Schedule Time</label>
                            <input
                              type="time"
                              value={round.schedule_time}
                              onChange={(e) => handleInterviewRoundChange(index, 'schedule_time', e.target.value)}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium mb-1">Panel Name</label>
                            <input
                              type="text"
                              value={round.panel_name}
                              onChange={(e) => handleInterviewRoundChange(index, 'panel_name', e.target.value)}
                              className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                              placeholder="Enter panel name"
                            />
                          </div>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium mb-1">Panel Comment/Score</label>
                          <textarea
                            value={round.panel_comment}
                            onChange={(e) => handleInterviewRoundChange(index, 'panel_comment', e.target.value)}
                            rows={2}
                            className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                            placeholder="Enter comments or score"
                          />
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Add Round Buttons */}
                <div className="flex flex-wrap gap-2">
                  {['L2 Round', 'L3 Round', 'L4 Round', 'L5 Round', 'L6 Round'].map((roundName) => (
                    !interviewRounds.some(r => r.round_name === roundName) && (
                      <button
                        key={roundName}
                        type="button"
                        onClick={() => addInterviewRound(roundName)}
                        className="px-3 py-1 text-xs border border-border rounded-md hover:bg-muted transition-colors"
                      >
                        Add {roundName}
                      </button>
                    )
                  ))}
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-border">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={creating || !fullName.trim()}
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  {creating ? (isEditing ? 'Updating...' : 'Creating...') : (isEditing ? 'Update Candidate' : 'Create Candidate')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
