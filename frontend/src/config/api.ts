// API Configuration
const getApiBaseUrl = (): string => {
  // Check if we're in production environment
  if (import.meta.env.VITE_APP_ENV === 'production') {
    return import.meta.env.VITE_API_BASE_URL || 'https://talenthero.bceglobaltech.com/api'
  }
  
  // Development environment
  return import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
}

export const API_BASE_URL = getApiBaseUrl()

// Helper function to build API URL
export const buildApiUrl = (endpoint: string): string => {
  if (endpoint.startsWith('http')) {
    return endpoint
  }
  return `${API_BASE_URL}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`
}

// Default fetch options with credentials
export const defaultFetchOptions: RequestInit = {
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
  },
}
