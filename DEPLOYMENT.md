# Deployment Guide for Talent Hero v3.11

This guide explains how to deploy Talent Hero v3.11 to work with both localhost (development) and your production domain `talenthero.bceglobaltech.com`.

## Overview

The application now supports dual environment configuration:
- **Development**: `http://localhost:5173` (frontend) + `http://localhost:8000` (backend)
- **Production**: `https://talenthero.bceglobaltech.com` (both frontend and backend)

## Quick Start

### For Development
```bash
# Switch to development environment
python switch_environment.py development

# Start backend
cd backend && python manage.py runserver

# Start frontend (in another terminal)
cd frontend && npm run dev
```

### For Production
```bash
# Build for production
python build_production.py

# Start backend for production
cd backend && python manage.py runserver 0.0.0.0:8000
```

## Environment Configuration

### Backend Configuration

The backend uses environment variables from `.env` files:

- **Development**: `backend/.env` (supports both localhost and domain)
- **Production**: `backend/.env.production` (domain-only configuration)

Key settings updated:
- `ALLOWED_HOSTS`: Now includes your domain
- `CORS_ALLOWED_ORIGINS`: Supports both localhost and domain
- `CSRF_TRUSTED_ORIGINS`: Supports both localhost and domain

### Frontend Configuration

The frontend uses Vite environment variables:

- **Development**: `frontend/.env`
- **Production**: `frontend/.env.production`

The frontend automatically detects the environment and uses the appropriate API base URL.

## File Changes Made

### New Files Created:
1. `frontend/.env` - Development environment variables
2. `frontend/.env.production` - Production environment variables
3. `frontend/src/config/api.ts` - API configuration with environment detection
4. `backend/.env.production` - Production backend configuration
5. `switch_environment.py` - Environment switching utility
6. `build_production.py` - Production build script
7. `nginx.conf.sample` - Sample nginx configuration
8. `DEPLOYMENT.md` - This deployment guide

### Files Modified:
1. `backend/.env` - Added domain support for development
2. `frontend/vite.config.ts` - Added host configuration
3. `frontend/src/components/SystemSettings.tsx` - Updated to use API configuration

## Nginx Configuration

1. Copy the sample nginx configuration:
   ```bash
   cp nginx.conf.sample /etc/nginx/sites-available/talenthero
   ```

2. Update the paths in the nginx configuration:
   - SSL certificate paths
   - Project root path
   - Static files paths

3. Enable the site:
   ```bash
   sudo ln -s /etc/nginx/sites-available/talenthero /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## SSL Certificate

Make sure you have SSL certificates for `talenthero.bceglobaltech.com`. You can use:
- Let's Encrypt (free)
- Your organization's SSL certificates
- Self-signed certificates (for testing)

## Environment Switching

Use the provided utility script to switch between environments:

```bash
# Switch to development
python switch_environment.py development
# or
python switch_environment.py dev

# Switch to production
python switch_environment.py production
# or
python switch_environment.py prod
```

## Production Deployment Steps

1. **Prepare the server**:
   ```bash
   # Install dependencies
   pip install -r backend/requirements.txt
   cd frontend && npm install
   ```

2. **Build for production**:
   ```bash
   python build_production.py
   ```

3. **Configure nginx** (see nginx configuration section above)

4. **Start the backend**:
   ```bash
   cd backend && python manage.py runserver 0.0.0.0:8000
   ```

5. **Access your application**:
   - Production: `https://talenthero.bceglobaltech.com`
   - Development: `http://localhost:5173`

## Troubleshooting

### CORS Issues
If you encounter CORS issues:
1. Check that your domain is in `CORS_ALLOWED_ORIGINS` in the backend `.env`
2. Verify nginx is properly forwarding headers
3. Check browser developer tools for specific CORS errors

### API Connection Issues
If the frontend can't connect to the backend:
1. Verify the `VITE_API_BASE_URL` in frontend `.env`
2. Check that the backend is running on the correct port
3. Verify nginx proxy configuration for `/api/` routes

### Environment Variables Not Loading
If environment variables aren't working:
1. Make sure you're using the correct `.env` file for your environment
2. Restart the development server after changing environment variables
3. Check that Vite environment variables start with `VITE_`

## Security Considerations

For production deployment:
1. Change the Django `SECRET_KEY` to a secure random value
2. Set `DEBUG=False` in production
3. Use HTTPS only (`CSRF_COOKIE_SECURE=True`, `SESSION_COOKIE_SECURE=True`)
4. Configure proper SSL certificates
5. Set up proper firewall rules
6. Consider using a process manager like systemd or supervisor for the backend

## Monitoring

Consider setting up monitoring for:
- Application logs
- Nginx access/error logs
- Database connections
- SSL certificate expiration
- Server resources (CPU, memory, disk)

## Support

If you encounter issues:
1. Check the logs in `/var/log/nginx/`
2. Check Django logs in the backend
3. Use browser developer tools to debug frontend issues
4. Verify environment variables are correctly set
