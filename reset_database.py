#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Reset Script for Talent Hero v3.11
Resets database migrations and resolves conflicts
"""

import os
import sys
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def reset_migrations():
    """Reset database migrations"""
    print("[INFO] Resetting database migrations...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("[ERROR] Backend directory not found")
        return False
    
    # Change to backend directory
    original_dir = os.getcwd()
    try:
        os.chdir(backend_dir)
        
        print("[INFO] Marking problematic migration as fake...")
        if sys.platform.startswith('win'):
            result = os.system("venv\\Scripts\\activate && python manage.py migrate ai_settings 0003 --fake")
        else:
            result = os.system("source venv/bin/activate && python manage.py migrate ai_settings 0003 --fake")
        
        if result == 0:
            print("[OK] Migration marked as fake")
        else:
            print("[WARN] Could not mark migration as fake (might be normal)")
        
        print("[INFO] Running all migrations...")
        if sys.platform.startswith('win'):
            result = os.system("venv\\Scripts\\activate && python manage.py migrate")
        else:
            result = os.system("source venv/bin/activate && python manage.py migrate")
        
        if result == 0:
            print("[OK] All migrations completed successfully")
            return True
        else:
            print("[ERROR] Migration failed")
            return False
            
    finally:
        os.chdir(original_dir)

def main():
    print("[INFO] Database Reset Tool for Talent Hero v3.11")
    print("[WARN] This will attempt to fix migration conflicts")
    
    response = input("Do you want to continue? (y/N): ")
    if response.lower() != 'y':
        print("[INFO] Operation cancelled")
        return
    
    if reset_migrations():
        print("\n[SUCCESS] Database migrations reset successfully!")
        print("\n[INFO] You can now run the build script:")
        print("   python build_production.py")
    else:
        print("\n[ERROR] Database reset failed")
        print("\n[INFO] You may need to manually fix the database:")
        print("   1. Connect to your PostgreSQL database")
        print("   2. Drop the problematic table: DROP TABLE ai_settings_aiproviderconfig;")
        print("   3. Run migrations again")

if __name__ == "__main__":
    main()
