@echo off
chcp 65001 >nul
echo [INFO] Starting Talent Hero v3.11 in development mode...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if Node.js is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js/npm is not installed or not in PATH
    pause
    exit /b 1
)

REM Switch to development environment
echo [STEP 1] Switching to development environment...
python switch_environment.py development
if errorlevel 1 (
    echo [ERROR] Failed to switch to development environment
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "backend\venv" (
    echo [WARN] Virtual environment not found at backend\venv
    echo [INFO] Creating virtual environment...
    cd backend
    python -m venv venv
    if errorlevel 1 (
        echo [ERROR] Failed to create virtual environment
        pause
        exit /b 1
    )
    echo [INFO] Installing backend dependencies...
    venv\Scripts\activate && pip install -r requirements.txt
    cd ..
    echo [OK] Virtual environment created and dependencies installed
)

REM Install frontend dependencies if needed
if not exist "frontend\node_modules" (
    echo [INFO] Installing frontend dependencies...
    cd frontend
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
    echo [OK] Frontend dependencies installed
)

echo.
echo [SUCCESS] Development environment is ready!
echo.
echo [INFO] To start the application:
echo   Backend:  cd backend ^&^& venv\Scripts\activate ^&^& python manage.py runserver
echo   Frontend: cd frontend ^&^& npm run dev
echo.
echo [INFO] Access the application at: http://localhost:5173
echo.
pause
