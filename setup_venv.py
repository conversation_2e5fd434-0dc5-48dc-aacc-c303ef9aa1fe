#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Virtual Environment Setup Script for Talent Hero v3.11
Creates and sets up the virtual environment if it doesn't exist
"""

import os
import subprocess
import sys
from pathlib import Path

# Set UTF-8 encoding for Windows
if sys.platform.startswith('win'):
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

def run_command(command, cwd=None, description=""):
    """Run a command and handle errors"""
    print(f"[INFO] {description}")
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, check=True, 
                              capture_output=True, text=True)
        print(f"[OK] {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def setup_virtual_environment():
    """Set up virtual environment in backend directory"""
    backend_dir = Path("backend")
    venv_path = backend_dir / "venv"
    
    if not backend_dir.exists():
        print("[ERROR] Backend directory not found")
        return False
    
    if venv_path.exists():
        print("[INFO] Virtual environment already exists at backend/venv")
        return True
    
    print("[INFO] Creating virtual environment...")
    
    # Create virtual environment
    if not run_command("python -m venv venv", cwd=backend_dir, 
                      description="Creating virtual environment"):
        return False
    
    # Install requirements
    if sys.platform.startswith('win'):
        pip_command = "venv\\Scripts\\activate && pip install -r requirements.txt"
    else:
        pip_command = "source venv/bin/activate && pip install -r requirements.txt"
    
    if not run_command(pip_command, cwd=backend_dir,
                      description="Installing Python dependencies"):
        return False
    
    return True

def main():
    print("[INFO] Setting up virtual environment for Talent Hero v3.11...")
    
    if setup_virtual_environment():
        print("\n[SUCCESS] Virtual environment setup completed!")
        print("\n[INFO] You can now run:")
        print("   python build_production.py  (for production)")
        print("   python switch_environment.py development  (for development)")
    else:
        print("\n[ERROR] Virtual environment setup failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
